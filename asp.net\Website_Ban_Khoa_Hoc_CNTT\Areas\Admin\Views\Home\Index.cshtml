@model AdminDashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<!-- Admin Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card stat-card-primary">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Users</div>
                        <div class="h4 mb-0 font-weight-bold">@Model.TotalUsers</div>
                        <small class="opacity-75">Người dùng đã đăng ký</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card stat-card-success">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Courses</div>
                        <div class="h4 mb-0 font-weight-bold">@Model.TotalCourses</div>
                        <small class="opacity-75">Khóa học có sẵn</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-graduation-cap fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card stat-card-warning">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Enrollments</div>
                        <div class="h4 mb-0 font-weight-bold">@Model.TotalEnrollments</div>
                        <small class="opacity-75">Lượt đăng ký khóa học</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-user-graduate fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card stat-card-danger">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Doanh thu</div>
                        <div class="h4 mb-0 font-weight-bold">@Model.TotalRevenue.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</div>
                        <small class="opacity-75">Doanh thu tích lũy</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="admin-card">
            <div class="card-header bg-transparent border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2 text-primary"></i>
                    Thống kê theo tháng
                </h5>
                <small class="text-muted">Enrollments và Revenue 6 tháng gần nhất</small>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="admin-card">
            <div class="card-header bg-transparent border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2 text-success"></i>
                    Tổng quan hệ thống
                </h5>
                <small class="text-muted">Phân bố dữ liệu</small>
            </div>
            <div class="card-body">
                <canvas id="overviewChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Admin Recent Activities -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="admin-card">
            <div class="card-header bg-transparent border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2 text-info"></i>
                    Users mới nhất
                </h5>
                <small class="text-muted">5 người dùng đăng ký gần đây</small>
            </div>
            <div class="card-body">
                @if (Model.RecentUsers.Any())
                {
                    <div class="admin-table">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">Tên</th>
                                    <th class="border-0">Email</th>
                                    <th class="border-0">Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model.RecentUsers)
                                {
                                    <tr>
                                        <td class="border-0">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                                <strong>@user.FullName</strong>
                                            </div>
                                        </td>
                                        <td class="border-0 text-muted">@user.Email</td>
                                        <td class="border-0">
                                            <span class="badge bg-light text-dark">@user.CreatedAt.ToString("dd/MM/yyyy")</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Chưa có user nào.</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="admin-card">
            <div class="card-header bg-transparent border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-graduation-cap me-2 text-success"></i>
                    Courses mới nhất
                </h5>
                <small class="text-muted">5 khóa học được tạo gần đây</small>
            </div>
            <div class="card-body">
                @if (Model.RecentCourses.Any())
                {
                    <div class="admin-table">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">Tên khóa học</th>
                                    <th class="border-0">Category</th>
                                    <th class="border-0">Giá</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var course in Model.RecentCourses)
                                {
                                    <tr>
                                        <td class="border-0">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-book text-white"></i>
                                                </div>
                                                <strong>@course.Title</strong>
                                            </div>
                                        </td>
                                        <td class="border-0">
                                            <span class="badge bg-secondary">@course.Category?.Name</span>
                                        </td>
                                        <td class="border-0">
                                            <span class="text-success fw-bold">@course.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Chưa có khóa học nào.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Monthly Chart
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.MonthlyStats.Select(s => s.Month))),
                datasets: [{
                    label: 'Enrollments',
                    data: @Html.Raw(Json.Serialize(Model.MonthlyStats.Select(s => s.Enrollments))),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Revenue (VND)',
                    data: @Html.Raw(Json.Serialize(Model.MonthlyStats.Select(s => s.Revenue))),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // Overview Chart
        const overviewCtx = document.getElementById('overviewChart').getContext('2d');
        const overviewChart = new Chart(overviewCtx, {
            type: 'doughnut',
            data: {
                labels: ['Users', 'Courses', 'Enrollments'],
                datasets: [{
                    data: [@Model.TotalUsers, @Model.TotalCourses, @Model.TotalEnrollments],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(255, 205, 86, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
}
