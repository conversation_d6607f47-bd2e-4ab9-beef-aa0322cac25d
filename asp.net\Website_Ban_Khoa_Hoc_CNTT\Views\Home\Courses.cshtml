@model CoursesViewModel
@{
    ViewData["Title"] = "Khóa học";
}

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12 text-center" data-aos="fade-up">
            <h1 class="display-4 fw-bold mb-3"><PERSON>h<PERSON><PERSON> học lập trình CNTT</h1>
            <p class="lead text-muted">Khám phá hàng trăm khóa học chất lượng cao từ các chuyên gia hàng đầu</p>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-lg-8 col-md-6 mb-3">
            <form method="get" class="d-flex">
                <input type="text" name="search" value="@Model.SearchTerm"
                       class="form-control me-2" placeholder="T<PERSON><PERSON> kiếm khóa học...">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <select class="form-select" onchange="filterByCategory(this.value)">
                <option value="">Tất cả danh mục</option>
                @foreach (var category in Model.Categories)
                {
                    <option value="@category.Id" selected="@(Model.CurrentCategoryId == category.Id)">
                        @category.Name
                    </option>
                }
            </select>
        </div>
    </div>

    <!-- Results Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <p class="text-muted mb-0">
                    Hiển thị @((Model.CurrentPage - 1) * Model.PageSize + 1) -
                    @Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCourses)
                    trong tổng số @Model.TotalCourses khóa học
                </p>
                <div class="d-flex align-items-center">
                    <label class="me-2">Sắp xếp:</label>
                    <select class="form-select form-select-sm" style="width: auto;">
                        <option>Mới nhất</option>
                        <option>Phổ biến nhất</option>
                        <option>Giá thấp đến cao</option>
                        <option>Giá cao đến thấp</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="row">
        @if (Model.Courses.Any())
        {
            @foreach (var course in Model.Courses)
            {
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="@(Model.Courses.ToList().IndexOf(course) * 100)">
                    <div class="card h-100 course-card">
                        <div class="position-relative">
                            <img src="@course.Thumbnail" class="card-img-top" alt="@course.Title">
                            @if (course.Price == 0)
                            {
                                <span class="position-absolute top-0 end-0 m-2 badge bg-success">Miễn phí</span>
                            }
                            else
                            {
                                <span class="position-absolute top-0 end-0 m-2 badge bg-primary">@course.Price.ToString("N0") VNĐ</span>
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-outline-primary">@course.Category.Name</span>
                                <span class="badge bg-outline-secondary ms-1">@course.Status</span>
                            </div>
                            <h5 class="card-title">@course.Title</h5>
                            <p class="card-text text-muted flex-grow-1">
                                @Html.Raw(course.Description.Length > 120 ? course.Description.Substring(0, 120) + "..." : course.Description)
                            </p>

                            <!-- Course Stats -->
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <small class="text-muted">
                                        <i class="fas fa-users"></i><br>
                                        @course.Enrollments.Count() học viên
                                    </small>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">
                                        <i class="fas fa-book"></i><br>
                                        @course.Chapters.Count() chương
                                    </small>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">
                                        <i class="fas fa-star text-warning"></i><br>
                                        4.8 (120)
                                    </small>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-auto">
                                <div class="d-grid gap-2">
                                    <a href="@Url.Action("CourseDetail", "Home", new { id = course.Id })"
                                       class="btn btn-primary">
                                        <i class="fas fa-eye me-2"></i>Xem chi tiết
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-5">
                <div data-aos="fade-up">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">Không tìm thấy khóa học nào</h3>
                    <p class="text-muted">Hãy thử tìm kiếm với từ khóa khác hoặc chọn danh mục khác.</p>
                    <a href="@Url.Action("Courses", "Home")" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>Xem tất cả khóa học
                    </a>
                </div>
            </div>
        }
    </div>

    <!-- Pagination -->
    @if (Model.TotalPages > 1)
    {
        <div class="row mt-5">
            <div class="col-12">
                <nav aria-label="Course pagination">
                    <ul class="pagination justify-content-center">
                        @if (Model.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" href="@Url.Action("Courses", new {
                                    page = Model.CurrentPage - 1,
                                    categoryId = Model.CurrentCategoryId,
                                    search = Model.SearchTerm
                                })">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        }

                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                <a class="page-link" href="@Url.Action("Courses", new {
                                    page = i,
                                    categoryId = Model.CurrentCategoryId,
                                    search = Model.SearchTerm
                                })">@i</a>
                            </li>
                        }

                        @if (Model.CurrentPage < Model.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" href="@Url.Action("Courses", new {
                                    page = Model.CurrentPage + 1,
                                    categoryId = Model.CurrentCategoryId,
                                    search = Model.SearchTerm
                                })">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            </div>
        </div>
    }
</div>

<style>
    .course-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    }

    .course-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .badge.bg-outline-primary {
        background-color: rgba(99, 102, 241, 0.1) !important;
        color: var(--primary-color) !important;
        border: 1px solid var(--primary-color);
    }

    .badge.bg-outline-secondary {
        background-color: rgba(108, 117, 125, 0.1) !important;
        color: #6c757d !important;
        border: 1px solid #6c757d;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: none;
        color: var(--primary-color);
    }

    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
</style>

<script>
    function filterByCategory(categoryId) {
        const url = new URL(window.location);
        if (categoryId) {
            url.searchParams.set('categoryId', categoryId);
        } else {
            url.searchParams.delete('categoryId');
        }
        url.searchParams.delete('page'); // Reset to first page
        window.location.href = url.toString();
    }

    // Course page functionality
    console.log('Courses page loaded successfully!');
</script>
