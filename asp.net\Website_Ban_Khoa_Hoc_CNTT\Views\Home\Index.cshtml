@model HomeViewModel
@{
    ViewData["Title"] = "Trang chủ";
}

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-4">
                    H<PERSON><PERSON> lập trình CNTT <br>
                    <span class="text-warning"><PERSON><PERSON> dàng & <PERSON><PERSON><PERSON> qu<PERSON></span>
                </h1>
                <p class="lead mb-4">
                    Khám phá hàng nghìn khóa học lập trình từ cơ bản đến nâng cao. 
                    Học cùng các chuyên gia hàng đầu và xây dựng sự nghiệp IT của bạn.
                </p>
                <div class="d-flex flex-wrap gap-3">
                    <a href="@Url.Action("Courses", "Home")" class="btn btn-secondary btn-lg">
                        <i class="fas fa-play me-2"></i>B<PERSON><PERSON> đầu học ngay
                    </a>
                    <a href="@Url.Action("About", "Home")" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-info-circle me-2"></i>Tìm hiểu thêm
                    </a>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="text-center">
                    <img src="https://via.placeholder.com/500x400/6366f1/ffffff?text=E-Learning" 
                         alt="E-Learning" class="img-fluid rounded-3 shadow-lg">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-book-open fa-3x text-primary mb-3"></i>
                        <h3 class="fw-bold text-primary">@Model.TotalCourses</h3>
                        <p class="text-muted">Khóa học</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-success mb-3"></i>
                        <h3 class="fw-bold text-success">@Model.TotalStudents</h3>
                        <p class="text-muted">Học viên</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-chalkboard-teacher fa-3x text-warning mb-3"></i>
                        <h3 class="fw-bold text-warning">@Model.TotalInstructors</h3>
                        <p class="text-muted">Giảng viên</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-certificate fa-3x text-info mb-3"></i>
                        <h3 class="fw-bold text-info">1000+</h3>
                        <p class="text-muted">Chứng chỉ</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Courses -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">Khóa học nổi bật</h2>
            <p class="lead text-muted">Những khóa học được đánh giá cao nhất từ cộng đồng học viên</p>
        </div>
        
        <div class="row">
            @foreach (var course in Model.FeaturedCourses.Take(6))
            {
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="@(Model.FeaturedCourses.ToList().IndexOf(course) * 100)">
                    <div class="card h-100">
                        <img src="@course.Thumbnail" class="card-img-top" alt="@course.Title">
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-primary">@course.Category.Name</span>
                            </div>
                            <h5 class="card-title">@course.Title</h5>
                            <p class="card-text text-muted flex-grow-1">
                                @Html.Raw(course.Description.Length > 100 ? course.Description.Substring(0, 100) + "..." : course.Description)
                            </p>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <div>
                                    @if (course.Price == 0)
                                    {
                                        <span class="price-badge price-free">Miễn phí</span>
                                    }
                                    else
                                    {
                                        <span class="price-badge">@course.Price.ToString("N0") VNĐ</span>
                                    }
                                </div>
                                <div class="rating">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <i class="fas fa-star"></i>
                                    }
                                    <small class="text-muted ms-1">(4.8)</small>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="@Url.Action("CourseDetail", "Home", new { id = course.Id })" 
                                   class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>Xem chi tiết
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        
        <div class="text-center mt-4" data-aos="fade-up">
            <a href="@Url.Action("Courses", "Home")" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-th-large me-2"></i>Xem tất cả khóa học
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">Danh mục khóa học</h2>
            <p class="lead text-muted">Khám phá các lĩnh vực công nghệ thông tin đa dạng</p>
        </div>
        
        <div class="row">
            @foreach (var category in Model.Categories.Take(8))
            {
                <div class="col-lg-3 col-md-6 mb-4" data-aos="zoom-in" data-aos-delay="@(Model.Categories.ToList().IndexOf(category) * 100)">
                    <a href="@Url.Action("Courses", "Home", new { categoryId = category.Id })" 
                       class="text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm category-card">
                            <div class="card-body">
                                <div class="mb-3">
                                    @switch (category.Name)
                                    {
                                        case "Lập trình Web":
                                            <i class="fas fa-globe fa-3x text-primary"></i>
                                            break;
                                        case "Lập trình Mobile":
                                            <i class="fas fa-mobile-alt fa-3x text-success"></i>
                                            break;
                                        case "Cơ sở dữ liệu":
                                            <i class="fas fa-database fa-3x text-info"></i>
                                            break;
                                        case "DevOps & Cloud":
                                            <i class="fas fa-cloud fa-3x text-warning"></i>
                                            break;
                                        case "AI & Machine Learning":
                                            <i class="fas fa-robot fa-3x text-danger"></i>
                                            break;
                                        case "Blockchain & Web3":
                                            <i class="fas fa-link fa-3x text-purple"></i>
                                            break;
                                        case "Bảo mật & Hacking":
                                            <i class="fas fa-shield-alt fa-3x text-dark"></i>
                                            break;
                                        default:
                                            <i class="fas fa-code fa-3x text-secondary"></i>
                                            break;
                                    }
                                </div>
                                <h5 class="card-title">@category.Name</h5>
                                <p class="card-text text-muted">@category.Description</p>
                            </div>
                        </div>
                    </a>
                </div>
            }
        </div>
    </div>
</section>

<!-- Latest Courses -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">Khóa học mới nhất</h2>
            <p class="lead text-muted">Cập nhật những kiến thức và công nghệ mới nhất</p>
        </div>
        
        <div class="row">
            @foreach (var course in Model.LatestCourses.Take(6))
            {
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="@(Model.LatestCourses.ToList().IndexOf(course) * 100)">
                    <div class="card h-100">
                        <img src="@course.Thumbnail" class="card-img-top" alt="@course.Title">
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-success">Mới</span>
                                <span class="badge bg-primary ms-1">@course.Category.Name</span>
                            </div>
                            <h5 class="card-title">@course.Title</h5>
                            <p class="card-text text-muted flex-grow-1">
                                @Html.Raw(course.Description.Length > 100 ? course.Description.Substring(0, 100) + "..." : course.Description)
                            </p>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <div>
                                    @if (course.Price == 0)
                                    {
                                        <span class="price-badge price-free">Miễn phí</span>
                                    }
                                    else
                                    {
                                        <span class="price-badge">@course.Price.ToString("N0") VNĐ</span>
                                    }
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    @course.CreatedAt.ToString("dd/MM/yyyy")
                                </small>
                            </div>
                            <div class="mt-3">
                                <a href="@Url.Action("CourseDetail", "Home", new { id = course.Id })" 
                                   class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>Xem chi tiết
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container text-center">
        <div class="row justify-content-center">
            <div class="col-lg-8" data-aos="fade-up">
                <h2 class="display-5 fw-bold mb-4">Sẵn sàng bắt đầu hành trình học tập?</h2>
                <p class="lead mb-4">
                    Tham gia cùng hàng nghìn học viên đã thành công trong việc phát triển kỹ năng lập trình. 
                    Đăng ký ngay hôm nay để nhận ưu đãi đặc biệt!
                </p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    @if (!User.Identity!.IsAuthenticated)
                    {
                        <a href="/Identity/Account/Register" class="btn btn-secondary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Đăng ký miễn phí
                        </a>
                    }
                    <a href="@Url.Action("Courses", "Home")" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-search me-2"></i>Khám phá khóa học
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .category-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .text-purple {
        color: #8b5cf6 !important;
    }
</style>
