# 🔐 TÀI KHOẢN MẶC ĐỊNH HỆ THỐNG

## 📋 DANH SÁCH TÀI KHOẢN

### 👑 T<PERSON><PERSON> kho<PERSON> (Quản trị viên)
- **Email:** <EMAIL>
- **Password:** Admin@123
- **Vai trò:** Administrator
- **Quyền hạn:** Toàn quyền quản trị hệ thống

**Chức năng có thể truy cập:**
- ✅ Dashboard quản trị
- ✅ Quản lý khóa học (CRUD)
- ✅ Quản lý người dùng
- ✅ Quản lý đăng ký học
- ✅ Quản lý tiến độ học tập
- ✅ Quản lý bình luận
- ✅ Quản lý khuyến mãi
- ✅ Quản lý chứng chỉ
- ✅ Xem báo cáo và thống kê
- ✅ Cấu hình hệ thống

### 🎓 <PERSON><PERSON><PERSON> khoản Instructor (Giảng viên)
- **Email:** <EMAIL>
- **Password:** Instructor@123
- **Vai trò:** Instructor
- **Quyền hạn:** Quản lý khóa học và học viên

**Chức năng có thể truy cập:**
- ✅ Tạo và quản lý khóa học của mình
- ✅ Xem danh sách học viên
- ✅ Theo dõi tiến độ học tập
- ✅ Trả lời bình luận học viên
- ✅ Tạo và quản lý bài học
- ✅ Xem thống kê khóa học

### 👨‍🎓 Tài khoản Student (Học viên)
- **Email:** <EMAIL>
- **Password:** Student@123
- **Vai trò:** User/Student
- **Quyền hạn:** Học viên thường

**Chức năng có thể truy cập:**
- ✅ Duyệt và tìm kiếm khóa học
- ✅ Đăng ký học khóa học
- ✅ Xem nội dung bài học
- ✅ Theo dõi tiến độ học tập
- ✅ Bình luận và đánh giá
- ✅ Quản lý hồ sơ cá nhân
- ✅ Xem chứng chỉ đã đạt được

## 🚀 CÁCH ĐĂNG NHẬP

### Đăng nhập Admin:
1. Truy cập: `http://localhost:5000/Admin`
2. Hoặc: `http://localhost:5000/Account/Login`
3. Nhập email: `<EMAIL>`
4. Nhập password: `Admin@123`
5. Click "Đăng nhập"
6. Tự động chuyển hướng đến Admin Dashboard

### Đăng nhập User/Student:
1. Truy cập: `http://localhost:5000/Account/Login`
2. Nhập email: `<EMAIL>`
3. Nhập password: `Student@123`
4. Click "Đăng nhập"
5. Chuyển hướng đến trang chủ

## 🔧 TẠO TÀI KHOẢN MỚI

### Tạo tài khoản qua giao diện:
1. Truy cập: `http://localhost:5000/Account/Register`
2. Điền thông tin:
   - Họ tên
   - Email
   - Mật khẩu (tối thiểu 6 ký tự)
   - Xác nhận mật khẩu
3. Click "Đăng ký"
4. Tài khoản mới sẽ có vai trò User mặc định

### Tạo tài khoản Admin mới:
1. Đăng nhập bằng tài khoản Admin hiện tại
2. Truy cập: `/Admin/Users`
3. Click "Tạo người dùng mới"
4. Điền thông tin và chọn vai trò "Admin"
5. Lưu thông tin

## ⚙️ PHÂN QUYỀN HỆ THỐNG

### Các vai trò (Roles):
1. **Administrator**
   - Toàn quyền quản trị
   - Truy cập tất cả chức năng
   - Quản lý người dùng khác

2. **Instructor**
   - Quản lý khóa học của mình
   - Xem thông tin học viên
   - Tương tác với học viên

3. **User/Student**
   - Học viên thường
   - Đăng ký và học khóa học
   - Tương tác cơ bản

### Thay đổi vai trò:
1. Đăng nhập Admin
2. Truy cập `/Admin/Users`
3. Tìm người dùng cần thay đổi
4. Click "Chỉnh sửa"
5. Chọn vai trò mới
6. Lưu thay đổi

## 🔒 BẢO MẬT TÀI KHOẢN

### Yêu cầu mật khẩu:
- Tối thiểu 6 ký tự
- Nên có chữ hoa, chữ thường
- Nên có số và ký tự đặc biệt
- Không sử dụng thông tin cá nhân

### Khuyến nghị bảo mật:
1. **Thay đổi password mặc định ngay lập tức**
2. Sử dụng password mạnh
3. Không chia sẻ thông tin đăng nhập
4. Đăng xuất sau khi sử dụng
5. Kiểm tra hoạt động đăng nhập định kỳ

## 🔄 KHÔI PHỤC MẬT KHẨU

### Nếu quên mật khẩu:
1. Truy cập trang đăng nhập
2. Click "Quên mật khẩu?"
3. Nhập email đã đăng ký
4. Kiểm tra email để nhận link reset
5. Tạo mật khẩu mới

### Reset password cho Admin:
Nếu quên password Admin, có thể:
1. Sử dụng SQL Server Management Studio
2. Truy cập bảng `AspNetUsers`
3. Tìm user admin
4. Reset password qua code hoặc tạo user admin mới

## 📊 THỐNG KÊ TÀI KHOẢN

### Xem thông tin tài khoản:
- **Admin:** Truy cập `/Admin/Users` để xem tất cả
- **User:** Truy cập `/Account/Manage` để xem thông tin cá nhân

### Thông tin hiển thị:
- Tên đầy đủ
- Email
- Vai trò
- Ngày tạo tài khoản
- Lần đăng nhập cuối
- Trạng thái tài khoản

## 🚨 XỬ LÝ SỰ CỐ

### Không đăng nhập được:
1. Kiểm tra email/password
2. Xóa cache và cookies browser
3. Thử trình duyệt khác
4. Kiểm tra kết nối database
5. Xem logs ứng dụng

### Tài khoản bị khóa:
1. Liên hệ Admin
2. Hoặc Admin truy cập `/Admin/Users`
3. Tìm tài khoản bị khóa
4. Mở khóa tài khoản

### Lỗi phân quyền:
1. Kiểm tra vai trò của tài khoản
2. Đảm bảo có quyền truy cập chức năng
3. Đăng xuất và đăng nhập lại
4. Liên hệ Admin nếu cần thiết

## 📝 GHI CHÚ QUAN TRỌNG

1. **Backup tài khoản Admin:** Luôn có ít nhất 2 tài khoản Admin
2. **Thay đổi thông tin mặc định:** Đổi email và password ngay lập tức
3. **Kiểm tra định kỳ:** Xem lại danh sách người dùng thường xuyên
4. **Xóa tài khoản không dùng:** Dọn dẹp tài khoản test/demo

## 📞 HỖ TRỢ

### Liên hệ khi cần hỗ trợ:
- **Email:** <EMAIL>
- **Hotline:** 1900-xxxx
- **Tài liệu:** Xem file `HUONG_DAN_SU_DUNG.md`

---

**⚠️ LƯU Ý:** Hãy thay đổi tất cả mật khẩu mặc định ngay sau khi cài đặt hệ thống!
