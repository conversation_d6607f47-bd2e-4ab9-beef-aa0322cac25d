@model PromotionCreateViewModel
@{
    ViewData["Title"] = "Tạo khuyến mãi mới";
}

<!-- Admin Create Promotion -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-plus me-2"></i>
            Tạo khuyến mãi mới
        </h2>
        <a asp-action="Index" class="admin-btn admin-btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Quay lại danh sách
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin khuyến mãi</h5>
                </div>
                <div class="admin-card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Code" class="form-label">Mã khuyến mãi <span class="text-danger">*</span></label>
                                <input asp-for="Code" class="form-control" placeholder="VD: SUMMER2024" style="text-transform: uppercase;">
                                <span asp-validation-for="Code" class="text-danger"></span>
                                <div class="form-text">Chỉ được chứa chữ hoa và số, không có khoảng trắng</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="DiscountPer" class="form-label">Phần trăm giảm giá (%) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input asp-for="DiscountPer" type="number" min="1" max="100" class="form-control" placeholder="20">
                                    <span class="input-group-text">%</span>
                                </div>
                                <span asp-validation-for="DiscountPer" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="MaxUses" class="form-label">Số lần sử dụng tối đa <span class="text-danger">*</span></label>
                                <input asp-for="MaxUses" type="number" min="1" class="form-control" placeholder="100">
                                <span asp-validation-for="MaxUses" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="CourseId" class="form-label">Khóa học áp dụng <span class="text-danger">*</span></label>
                                <select asp-for="CourseId" class="form-select">
                                    <option value="">-- Chọn khóa học --</option>
                                    @foreach (var course in Model.AvailableCourses)
                                    {
                                        <option value="@course.Id">@course.Title - @course.Price.ToString("N0") VNĐ</option>
                                    }
                                </select>
                                <span asp-validation-for="CourseId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="StartDate" class="form-label">Ngày bắt đầu</label>
                                <input asp-for="StartDate" type="datetime-local" class="form-control">
                                <span asp-validation-for="StartDate" class="text-danger"></span>
                                <div class="form-text">Để trống nếu có hiệu lực ngay lập tức</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="EndDate" class="form-label">Ngày kết thúc</label>
                                <input asp-for="EndDate" type="datetime-local" class="form-control">
                                <span asp-validation-for="EndDate" class="text-danger"></span>
                                <div class="form-text">Để trống nếu không giới hạn thời gian</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" checked>
                                <label asp-for="IsActive" class="form-check-label">
                                    Kích hoạt ngay sau khi tạo
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Hủy bỏ
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Tạo khuyến mãi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Preview Card -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Xem trước khuyến mãi</h5>
                </div>
                <div class="admin-card-body">
                    <div class="promotion-preview">
                        <div class="text-center mb-3">
                            <div class="badge bg-primary fs-6 mb-2" id="preview-code">SUMMER2024</div>
                            <div class="h4 text-success" id="preview-discount">20% OFF</div>
                        </div>
                        
                        <div class="border rounded p-3 bg-light">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="text-muted small">Số lần sử dụng</div>
                                    <div class="fw-bold" id="preview-uses">0/100</div>
                                </div>
                                <div class="col-6">
                                    <div class="text-muted small">Khóa học</div>
                                    <div class="fw-bold small" id="preview-course">Chưa chọn</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="text-muted small">Thời gian hiệu lực:</div>
                            <div id="preview-time" class="fw-bold small">Ngay lập tức - Không giới hạn</div>
                        </div>

                        <div class="mt-3">
                            <div class="text-muted small">Trạng thái:</div>
                            <span id="preview-status" class="badge bg-success">Đang hoạt động</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tips Card -->
            <div class="admin-card mt-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">
                        <i class="fas fa-lightbulb me-2"></i>
                        Gợi ý
                    </h5>
                </div>
                <div class="admin-card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Sử dụng mã dễ nhớ và có ý nghĩa
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Đặt giới hạn số lần sử dụng hợp lý
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Thiết lập thời gian hiệu lực rõ ràng
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            Kiểm tra kỹ thông tin trước khi lưu
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Auto uppercase code input
            $('#Code').on('input', function() {
                this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
                updatePreview();
            });

            // Update preview when inputs change
            $('#DiscountPer, #MaxUses, #CourseId, #StartDate, #EndDate, #IsActive').on('change input', updatePreview);

            // Initial preview update
            updatePreview();
        });

        function updatePreview() {
            var code = $('#Code').val() || 'SUMMER2024';
            var discount = $('#DiscountPer').val() || '20';
            var maxUses = $('#MaxUses').val() || '100';
            var courseId = $('#CourseId').val();
            var courseName = courseId ? $('#CourseId option:selected').text().split(' - ')[0] : 'Chưa chọn';
            var startDate = $('#StartDate').val();
            var endDate = $('#EndDate').val();
            var isActive = $('#IsActive').is(':checked');

            $('#preview-code').text(code);
            $('#preview-discount').text(discount + '% OFF');
            $('#preview-uses').text('0/' + maxUses);
            $('#preview-course').text(courseName.length > 20 ? courseName.substring(0, 20) + '...' : courseName);

            // Update time display
            var timeText = '';
            if (startDate && endDate) {
                timeText = formatDate(startDate) + ' - ' + formatDate(endDate);
            } else if (startDate) {
                timeText = 'Từ ' + formatDate(startDate);
            } else if (endDate) {
                timeText = 'Đến ' + formatDate(endDate);
            } else {
                timeText = 'Ngay lập tức - Không giới hạn';
            }
            $('#preview-time').text(timeText);

            // Update status
            var statusBadge = $('#preview-status');
            if (isActive) {
                statusBadge.removeClass('bg-secondary').addClass('bg-success').text('Đang hoạt động');
            } else {
                statusBadge.removeClass('bg-success').addClass('bg-secondary').text('Không hoạt động');
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            var date = new Date(dateString);
            return date.toLocaleDateString('vi-VN');
        }

        // Form validation
        $('form').on('submit', function(e) {
            var startDate = $('#StartDate').val();
            var endDate = $('#EndDate').val();

            if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
                e.preventDefault();
                toastr.error('Ngày kết thúc phải sau ngày bắt đầu');
                return false;
            }
        });
    </script>
}
