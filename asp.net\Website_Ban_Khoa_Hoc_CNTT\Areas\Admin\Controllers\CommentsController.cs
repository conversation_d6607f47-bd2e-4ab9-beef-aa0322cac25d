using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ELearningWebsite.Data;
using ELearningWebsite.Models;
using ELearningWebsite.Areas.Admin.ViewModels;
using Microsoft.AspNetCore.Authorization;

namespace ELearningWebsite.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class CommentsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CommentsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Admin/Comments
        public async Task<IActionResult> Index(int page = 1, string searchTerm = "", int? lessonId = null, 
            int? userId = null, string status = "")
        {
            var viewModel = new CommentIndexViewModel
            {
                CurrentPage = page,
                SearchTerm = searchTerm,
                LessonId = lessonId,
                UserId = userId,
                Status = status
            };

            // Build query
            var query = _context.Comments.AsQueryable();

            // Apply search filter
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c => c.Content.Contains(searchTerm));
            }

            // Apply lesson filter
            if (lessonId.HasValue)
            {
                query = query.Where(c => c.LessonId == lessonId.Value);
            }

            // Apply user filter
            if (userId.HasValue)
            {
                query = query.Where(c => c.UserId == userId.Value);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(status))
            {
                switch (status.ToLower())
                {
                    case "active":
                        query = query.Where(c => !c.IsDelete);
                        break;
                    case "deleted":
                        query = query.Where(c => c.IsDelete);
                        break;
                    // "all" - no filter
                }
            }

            // Get total count
            viewModel.TotalItems = await query.CountAsync();
            viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.TotalItems / viewModel.PageSize);

            // Get paginated results
            var comments = await query
                .OrderByDescending(c => c.CreatedAt)
                .Skip((page - 1) * viewModel.PageSize)
                .Take(viewModel.PageSize)
                .Select(c => new CommentListItem
                {
                    Id = c.Id,
                    LessonId = c.LessonId,
                    LessonTitle = "Lesson " + c.LessonId, // Simplified since we don't have Lesson table
                    UserId = c.UserId,
                    UserName = c.UserId.HasValue ? "User " + c.UserId.Value : "Anonymous",
                    UserEmail = c.UserId.HasValue ? "user" + c.UserId.Value + "@example.com" : "N/A",
                    ParentCommentId = c.ParentCommentId,
                    ParentCommentContent = c.ParentComment != null ? c.ParentComment.Content : "",
                    Content = c.Content ?? "",
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    IsDelete = c.IsDelete,
                    RepliesCount = c.Replies.Count(r => !r.IsDelete),
                    IsReply = c.ParentCommentId.HasValue
                })
                .ToListAsync();

            viewModel.Comments = comments;

            // Load available lessons and users for filters
            await LoadFilterOptions(viewModel);

            return View(viewModel);
        }

        // GET: Admin/Comments/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var comment = await _context.Comments
                .Include(c => c.ParentComment)
                .Include(c => c.Replies.Where(r => !r.IsDelete))
                .FirstOrDefaultAsync(c => c.Id == id);

            if (comment == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy bình luận";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new CommentDetailsViewModel
            {
                Id = comment.Id,
                LessonId = comment.LessonId,
                LessonTitle = "Lesson " + comment.LessonId,
                CourseTitle = "Course Title", // Simplified
                UserId = comment.UserId,
                UserName = comment.UserId.HasValue ? "User " + comment.UserId.Value : "Anonymous",
                UserEmail = comment.UserId.HasValue ? "user" + comment.UserId.Value + "@example.com" : "N/A",
                UserAvatar = "/images/default-avatar.png",
                ParentCommentId = comment.ParentCommentId,
                ParentCommentContent = comment.ParentComment?.Content ?? "",
                ParentUserName = comment.ParentComment?.UserId.HasValue == true ? 
                    "User " + comment.ParentComment.UserId.Value : "",
                Content = comment.Content ?? "",
                CreatedAt = comment.CreatedAt,
                UpdatedAt = comment.UpdatedAt,
                IsDelete = comment.IsDelete,
                Replies = comment.Replies.Select(r => new CommentReply
                {
                    Id = r.Id,
                    UserId = r.UserId,
                    UserName = r.UserId.HasValue ? "User " + r.UserId.Value : "Anonymous",
                    UserEmail = r.UserId.HasValue ? "user" + r.UserId.Value + "@example.com" : "N/A",
                    Content = r.Content ?? "",
                    CreatedAt = r.CreatedAt,
                    IsDelete = r.IsDelete
                }).ToList()
            };

            return View(viewModel);
        }

        // GET: Admin/Comments/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var comment = await _context.Comments
                .Include(c => c.ParentComment)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (comment == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy bình luận";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new CommentEditViewModel
            {
                Id = comment.Id,
                Content = comment.Content ?? "",
                LessonId = comment.LessonId,
                LessonTitle = "Lesson " + comment.LessonId,
                UserId = comment.UserId,
                UserName = comment.UserId.HasValue ? "User " + comment.UserId.Value : "Anonymous",
                ParentCommentId = comment.ParentCommentId,
                ParentCommentContent = comment.ParentComment?.Content ?? "",
                CreatedAt = comment.CreatedAt,
                UpdatedAt = comment.UpdatedAt,
                IsDelete = comment.IsDelete
            };

            return View(viewModel);
        }

        // POST: Admin/Comments/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CommentEditViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var comment = await _context.Comments.FindAsync(id);
                    if (comment == null)
                    {
                        TempData["ErrorMessage"] = "Không tìm thấy bình luận";
                        return RedirectToAction(nameof(Index));
                    }

                    comment.Content = model.Content;
                    comment.UpdatedAt = DateTime.Now;

                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "Cập nhật bình luận thành công";
                    return RedirectToAction(nameof(Details), new { id = comment.Id });
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }

            // Reload data if validation fails
            var originalComment = await _context.Comments
                .Include(c => c.ParentComment)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (originalComment != null)
            {
                model.LessonTitle = "Lesson " + originalComment.LessonId;
                model.UserName = originalComment.UserId.HasValue ? "User " + originalComment.UserId.Value : "Anonymous";
                model.ParentCommentContent = originalComment.ParentComment?.Content ?? "";
            }

            return View(model);
        }

        // GET: Admin/Comments/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var comment = await _context.Comments
                .Include(c => c.Replies)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (comment == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy bình luận";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new CommentDeleteViewModel
            {
                Id = comment.Id,
                Content = comment.Content ?? "",
                LessonTitle = "Lesson " + comment.LessonId,
                UserName = comment.UserId.HasValue ? "User " + comment.UserId.Value : "Anonymous",
                CreatedAt = comment.CreatedAt,
                RepliesCount = comment.Replies.Count(r => !r.IsDelete)
            };

            return View(viewModel);
        }

        // POST: Admin/Comments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var comment = await _context.Comments
                    .Include(c => c.Replies)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (comment == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy bình luận";
                    return RedirectToAction(nameof(Index));
                }

                // Soft delete - mark as deleted instead of removing from database
                comment.IsDelete = true;
                comment.UpdatedAt = DateTime.Now;

                // Also soft delete all replies
                foreach (var reply in comment.Replies.Where(r => !r.IsDelete))
                {
                    reply.IsDelete = true;
                    reply.UpdatedAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Xóa bình luận thành công";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                return RedirectToAction(nameof(Delete), new { id });
            }
        }

        // POST: Admin/Comments/Restore/5
        [HttpPost]
        public async Task<IActionResult> Restore(int id)
        {
            try
            {
                var comment = await _context.Comments.FindAsync(id);
                if (comment == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy bình luận" });
                }

                comment.IsDelete = false;
                comment.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Khôi phục bình luận thành công" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        // GET: Admin/Comments/Statistics
        public async Task<IActionResult> Statistics()
        {
            var now = DateTime.Now;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(now.Year, now.Month, 1);

            var stats = new CommentStatisticsViewModel
            {
                TotalComments = await _context.Comments.CountAsync(),
                ActiveComments = await _context.Comments.CountAsync(c => !c.IsDelete),
                DeletedComments = await _context.Comments.CountAsync(c => c.IsDelete),
                TotalReplies = await _context.Comments.CountAsync(c => c.ParentCommentId.HasValue && !c.IsDelete),
                CommentsToday = await _context.Comments.CountAsync(c => c.CreatedAt.Date == today && !c.IsDelete),
                CommentsThisWeek = await _context.Comments.CountAsync(c => c.CreatedAt.Date >= weekStart && !c.IsDelete),
                CommentsThisMonth = await _context.Comments.CountAsync(c => c.CreatedAt.Date >= monthStart && !c.IsDelete)
            };

            // Get trend data for last 7 days
            var trendData = new List<CommentTrendData>();
            for (int i = 6; i >= 0; i--)
            {
                var date = today.AddDays(-i);
                var count = await _context.Comments.CountAsync(c => c.CreatedAt.Date == date && !c.IsDelete);
                trendData.Add(new CommentTrendData { Date = date, Count = count });
            }
            stats.TrendData = trendData;

            // Get top commenters
            var topCommenters = await _context.Comments
                .Where(c => !c.IsDelete && c.UserId.HasValue)
                .GroupBy(c => c.UserId)
                .Select(g => new TopCommenterData
                {
                    UserId = g.Key.Value,
                    UserName = "User " + g.Key.Value,
                    UserEmail = "user" + g.Key.Value + "@example.com",
                    CommentCount = g.Count()
                })
                .OrderByDescending(x => x.CommentCount)
                .Take(10)
                .ToListAsync();
            stats.TopCommenters = topCommenters;

            // Get top lessons by comment count
            var topLessons = await _context.Comments
                .Where(c => !c.IsDelete)
                .GroupBy(c => c.LessonId)
                .Select(g => new TopLessonData
                {
                    LessonId = g.Key,
                    LessonTitle = "Lesson " + g.Key,
                    CourseTitle = "Course Title",
                    CommentCount = g.Count()
                })
                .OrderByDescending(x => x.CommentCount)
                .Take(10)
                .ToListAsync();
            stats.TopLessons = topLessons;

            return View(stats);
        }

        private async Task LoadFilterOptions(CommentIndexViewModel viewModel)
        {
            // Load available lessons (simplified since we don't have Lesson table)
            var lessonIds = await _context.Comments
                .Select(c => c.LessonId)
                .Distinct()
                .OrderBy(id => id)
                .ToListAsync();

            viewModel.AvailableLessons = lessonIds.Select(id => new LessonOption
            {
                Id = id,
                Title = "Lesson " + id,
                CourseTitle = "Course Title"
            }).ToList();

            // Load available users
            var userIds = await _context.Comments
                .Where(c => c.UserId.HasValue)
                .Select(c => c.UserId.Value)
                .Distinct()
                .OrderBy(id => id)
                .ToListAsync();

            viewModel.AvailableUsers = userIds.Select(id => new UserOption
            {
                Id = id,
                Name = "User " + id,
                Email = "user" + id + "@example.com"
            }).ToList();
        }
    }
}
