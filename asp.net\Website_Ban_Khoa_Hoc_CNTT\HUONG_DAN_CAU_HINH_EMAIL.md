# 📧 Hướng dẫn cấu hình Gmail để gửi email quên mật khẩu

## 🔧 Bước 1: Tạo App Password cho Gmail

### 1.1. B<PERSON>t xác thực 2 bước (2FA)
1. <PERSON><PERSON><PERSON> nhập vào Gmail
2. Vào **Google Account** → **Security**
3. Tìm **2-Step Verification** và bật nó lên
4. Làm theo hướng dẫn để thiết lập

### 1.2. Tạo App Password
1. <PERSON>u khi bật 2FA, vào **Security** → **App passwords**
2. Chọn **Mail** và **Windows Computer** (hoặc Other)
3. Nhập tên: "ELearning Website"
4. Click **Generate**
5. **Copy** mật khẩu 16 ký tự được tạo ra

## 🔧 Bước 2: Cấu hình appsettings.json

Mở file `appsettings.json` và thay đổi:

```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",        // ← Thay bằng email của bạn
    "SenderName": "ELearning CNTT Website",
    "Username": "<EMAIL>",           // ← Thay bằng email của bạn
    "Password": "your-16-digit-app-password"      // ← Thay bằng App Password vừa tạo
  }
}
```

## 🔧 Bước 3: Test chức năng

1. Chạy website: `dotnet run`
2. Vào trang đăng nhập: `http://localhost:5000/Identity/Account/Login`
3. Click **"Quên mật khẩu?"**
4. Nhập email và click **"Gửi liên kết đặt lại mật khẩu"**
5. Kiểm tra email để nhận liên kết reset password

## 🔧 Bước 4: Tạo trang ResetPassword (nếu chưa có)

Nếu cần, tôi sẽ tạo thêm trang ResetPassword để hoàn thiện chức năng.

## ⚠️ Lưu ý bảo mật

1. **KHÔNG** commit App Password lên Git
2. Sử dụng **Environment Variables** cho production:
   ```bash
   set EmailSettings__Password=your-app-password
   ```
3. Hoặc sử dụng **Azure Key Vault** cho production

## 🎯 Kết quả

Sau khi cấu hình xong:
- ✅ User có thể reset password qua email
- ✅ Email được gửi với template đẹp
- ✅ Liên kết reset có thời hạn 24h
- ✅ Bảo mật cao với App Password

## 🆘 Troubleshooting

### Lỗi "Authentication failed"
- Kiểm tra App Password có đúng không
- Đảm bảo đã bật 2FA

### Lỗi "SMTP timeout"
- Kiểm tra kết nối internet
- Thử port 465 với SSL thay vì 587

### Email không nhận được
- Kiểm tra thư mục Spam/Junk
- Kiểm tra email có tồn tại không
