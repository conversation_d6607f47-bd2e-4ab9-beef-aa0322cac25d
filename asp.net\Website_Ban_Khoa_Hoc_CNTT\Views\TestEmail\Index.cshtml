@{
    ViewData["Title"] = "Test Email Configuration";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-envelope-open-text me-2"></i>
                        Test Email Configuration
                    </h4>
                </div>
                <div class="card-body">
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Success))
                    {
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            @ViewBag.Success
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Error))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @ViewBag.Error
                        </div>
                    }

                    <!-- <PERSON>ail Settings Display -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">C<PERSON>u hình Email hiện tại</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>SMTP Server:</strong> @ViewBag.EmailSettings.SmtpServer</p>
                                    <p><strong>Port:</strong> @ViewBag.EmailSettings.SmtpPort</p>
                                    <p><strong>Sender Email:</strong> @ViewBag.EmailSettings.SenderEmail</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Sender Name:</strong> @ViewBag.EmailSettings.SenderName</p>
                                    <p><strong>Username:</strong> @ViewBag.EmailSettings.Username</p>
                                    <p><strong>Password:</strong> @(string.IsNullOrEmpty(ViewBag.EmailSettings.Password) ? "❌ Chưa cấu hình" : "✅ Đã cấu hình")</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Form -->
                    <form method="post" action="/TestEmail/Send">
                        <div class="mb-3">
                            <label for="testEmail" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                Email để test
                            </label>
                            <input type="email" class="form-control" id="testEmail" name="testEmail" 
                                   placeholder="Nhập email để nhận email test" required>
                            <div class="form-text">
                                Nhập email của bạn để test xem có nhận được email không
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>
                                Gửi Email Test
                            </button>
                        </div>
                    </form>

                    <!-- Troubleshooting -->
                    <div class="mt-4">
                        <h5>🔧 Troubleshooting</h5>
                        <div class="alert alert-warning">
                            <h6>Nếu không nhận được email, hãy kiểm tra:</h6>
                            <ul class="mb-0">
                                <li>✅ Gmail đã bật 2FA (xác thực 2 bước)</li>
                                <li>✅ Đã tạo App Password (không phải mật khẩu Gmail thường)</li>
                                <li>✅ App Password đã được copy chính xác vào appsettings.json</li>
                                <li>✅ Email và Username trong appsettings.json giống nhau</li>
                                <li>✅ Kiểm tra thư mục Spam/Junk</li>
                                <li>✅ Kiểm tra logs trong console để xem lỗi cụ thể</li>
                            </ul>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-1"></i>
                            Về trang chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
