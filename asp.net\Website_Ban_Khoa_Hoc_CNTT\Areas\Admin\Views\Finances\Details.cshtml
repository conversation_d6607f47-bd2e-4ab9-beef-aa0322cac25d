@model Finance
@{
    ViewData["Title"] = "Chi tiết Tài chính";
}

<!-- Admin Finance Details -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-file-invoice me-2"></i>
            Chi tiết Tài chính #@Model.Id
        </h2>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.Id" class="admin-btn admin-btn-primary me-2">
                <i class="fas fa-edit me-2"></i>
                Chỉnh sửa
            </a>
            <a asp-action="Index" class="admin-btn admin-btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Finance Information -->
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin tài chính</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>ID:</strong></td>
                                    <td>@Model.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>Tháng/Năm:</strong></td>
                                    <td>
                                        <span class="badge bg-info fs-6">@Model.Month/@Model.Year</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Doanh thu:</strong></td>
                                    <td>
                                        <span class="fw-bold text-success fs-5">@Model.Revenue.ToString("N0") VNĐ</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Phí:</strong></td>
                                    <td>
                                        <span class="fw-bold text-warning fs-5">@Model.Fee.ToString("N0") VNĐ</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Doanh thu ròng:</strong></td>
                                    <td>
                                        @{
                                            var netRevenue = Model.Revenue - Model.Fee;
                                            var netClass = netRevenue >= 0 ? "text-primary" : "text-danger";
                                        }
                                        <span class="fw-bold @netClass fs-4">@netRevenue.ToString("N0") VNĐ</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Loại:</strong></td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(Model.Type))
                                        {
                                            <span class="badge bg-secondary fs-6">@Model.Type</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa phân loại</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label"><strong>Mô tả:</strong></label>
                                <div class="border rounded p-3 bg-light">
                                    @if (!string.IsNullOrEmpty(Model.Description))
                                    {
                                        <p class="mb-0">@Model.Description</p>
                                    }
                                    else
                                    {
                                        <p class="text-muted mb-0">Chưa có mô tả</p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audit Information -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin kiểm toán</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Ngày tạo:</strong></td>
                                    <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                </tr>
                                <tr>
                                    <td><strong>Người tạo:</strong></td>
                                    <td>@Model.CreatedBy</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Ngày cập nhật:</strong></td>
                                    <td>@Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                </tr>
                                <tr>
                                    <td><strong>Người cập nhật:</strong></td>
                                    <td>@Model.UpdatedBy</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions & Statistics -->
        <div class="col-lg-4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thao tác</h5>
                </div>
                <div class="admin-card-body">
                    <div class="d-grid gap-2">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            Chỉnh sửa bản ghi
                        </a>
                        
                        <button type="button" class="btn btn-danger" onclick="deleteFinance(@Model.Id)">
                            <i class="fas fa-trash me-2"></i>
                            Xóa bản ghi
                        </button>
                        
                        <a asp-action="Create" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            Thêm bản ghi mới
                        </a>
                    </div>
                </div>
            </div>

            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Phân tích</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border rounded p-3">
                                @{
                                    var feePercentage = Model.Revenue > 0 ? (Model.Fee / Model.Revenue * 100) : 0;
                                }
                                <h5 class="text-warning mb-1">@feePercentage.ToString("F1")%</h5>
                                <small class="text-muted">Tỷ lệ phí</small>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="border rounded p-3">
                                @{
                                    var netPercentage = Model.Revenue > 0 ? (netRevenue / Model.Revenue * 100) : 0;
                                }
                                <h5 class="text-primary mb-1">@netPercentage.ToString("F1")%</h5>
                                <small class="text-muted">Tỷ lệ doanh thu ròng</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Visual representation -->
                    <div class="mt-3">
                        <label class="form-label"><strong>Phân bổ doanh thu:</strong></label>
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: @netPercentage%" 
                                 aria-valuenow="@netPercentage" 
                                 aria-valuemin="0" aria-valuemax="100">
                                Ròng: @netPercentage.ToString("F1")%
                            </div>
                            <div class="progress-bar bg-warning" role="progressbar" 
                                 style="width: @feePercentage%" 
                                 aria-valuenow="@feePercentage" 
                                 aria-valuemin="0" aria-valuemax="100">
                                Phí: @feePercentage.ToString("F1")%
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-success">
                                <i class="fas fa-square"></i> Doanh thu ròng: @netRevenue.ToString("N0") VNĐ
                            </small>
                            <small class="text-warning">
                                <i class="fas fa-square"></i> Phí: @Model.Fee.ToString("N0") VNĐ
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin bổ sung</h5>
                </div>
                <div class="admin-card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Ghi chú:</h6>
                        <ul class="mb-0">
                            <li>Bản ghi này thuộc tháng @Model.Month năm @Model.Year</li>
                            <li>Doanh thu ròng = Doanh thu - Phí</li>
                            <li>Tỷ lệ phí = (Phí / Doanh thu) × 100%</li>
                        </ul>
                    </div>

                    @if (netRevenue < 0)
                    {
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Cảnh báo:</h6>
                            <p class="mb-0">
                                Doanh thu ròng âm! Phí cao hơn doanh thu.
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@Html.AntiForgeryToken()

<script>
function deleteFinance(id) {
    if (confirm('Bạn có chắc chắn muốn xóa bản ghi tài chính này? Hành động này không thể hoàn tác!')) {
        const token = $('input[name="__RequestVerificationToken"]').val();
        
        $.ajax({
            url: '@Url.Action("Delete")',
            type: 'POST',
            data: {
                id: id,
                __RequestVerificationToken: token
            },
            success: function(response) {
                if (response.success) {
                    window.location.href = '@Url.Action("Index")';
                } else {
                    alert('Lỗi: ' + response.message);
                }
            },
            error: function() {
                alert('Có lỗi xảy ra khi xóa bản ghi');
            }
        });
    }
}
</script>
