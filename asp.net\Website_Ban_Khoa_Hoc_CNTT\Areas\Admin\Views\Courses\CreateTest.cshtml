@{
    ViewData["Title"] = "Test Thêm khóa học";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Test Thêm khóa học mới</h3>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger">
                            @TempData["ErrorMessage"]
                        </div>
                    }

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success">
                            @TempData["SuccessMessage"]
                        </div>
                    }

                    <form asp-action="CreateWithImage" method="post" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label for="Title" class="form-label">Tên khóa học <span class="text-danger">*</span></label>
                            <input name="Title" type="text" class="form-control" placeholder="Nhập tên khóa học" required />
                        </div>

                        <div class="form-group mb-3">
                            <label for="CategoryId" class="form-label">Danh mục <span class="text-danger">*</span></label>
                            <select name="CategoryId" class="form-control" required>
                                <option value="">-- Chọn danh mục --</option>
                                @if (ViewBag.Categories != null)
                                {
                                    @foreach (var category in (List<Category>)ViewBag.Categories)
                                    {
                                        <option value="@category.Id">@category.Name</option>
                                    }
                                }
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="Price" class="form-label">Giá (VNĐ) <span class="text-danger">*</span></label>
                            <input name="Price" type="number" class="form-control" placeholder="0" min="0" required />
                        </div>

                        <div class="form-group mb-3">
                            <label for="Description" class="form-label">Mô tả</label>
                            <textarea name="Description" class="form-control" rows="3" placeholder="Nhập mô tả khóa học"></textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="ThumbnailFile" class="form-label">Hình ảnh khóa học</label>
                            <input name="ThumbnailFile" id="ThumbnailFile" type="file" class="form-control" accept="image/*" />
                            <div class="form-text">Chọn file hình ảnh (JPG, PNG, GIF). Tối đa 5MB.</div>

                            <!-- Preview hình ảnh -->
                            <div id="imagePreview" class="mt-3" style="display: none;">
                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="ThumbnailUrl" class="form-label">Hoặc nhập URL hình ảnh</label>
                            <input name="ThumbnailUrl" type="url" class="form-control" placeholder="https://example.com/image.jpg" />
                            <div class="form-text">Nếu không upload file, có thể nhập URL hình ảnh từ internet.</div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="PreviewVideo" class="form-label">URL Video Preview</label>
                            <input name="PreviewVideo" type="url" class="form-control" placeholder="https://youtube.com/watch?v=..." />
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu khóa học
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preview hình ảnh khi chọn file
        document.getElementById('ThumbnailFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Kiểm tra file type
                if (!file.type.startsWith('image/')) {
                    alert('Vui lòng chọn file hình ảnh!');
                    this.value = '';
                    document.getElementById('imagePreview').style.display = 'none';
                    return;
                }

                // Kiểm tra file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File hình ảnh không được vượt quá 5MB!');
                    this.value = '';
                    document.getElementById('imagePreview').style.display = 'none';
                    return;
                }

                // Hiển thị preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                document.getElementById('imagePreview').style.display = 'none';
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.querySelector('input[name="Title"]').value.trim();
            const categoryId = document.querySelector('select[name="CategoryId"]').value;
            const price = document.querySelector('input[name="Price"]').value;

            if (!title) {
                e.preventDefault();
                alert('Vui lòng nhập tên khóa học');
                return;
            }

            if (!categoryId) {
                e.preventDefault();
                alert('Vui lòng chọn danh mục');
                return;
            }

            if (!price || price < 0) {
                e.preventDefault();
                alert('Vui lòng nhập giá hợp lệ');
                return;
            }
        });
    });
</script>
