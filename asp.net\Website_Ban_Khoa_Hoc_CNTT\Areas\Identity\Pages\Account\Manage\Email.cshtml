@page
@model EmailModel
@{
    ViewData["Title"] = "Quản lý Email";
    ViewData["ActivePage"] = "Email";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <partial name="_ManageNav" />
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        Quản lý Email
                    </h4>
                </div>
                <div class="card-body">
                    <partial name="_StatusMessage" for="StatusMessage" />
                    
                    <form id="email-form" method="post">
                        <div asp-validation-summary="All" class="text-danger"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">Email hiện tại</label>
                            <input asp-for="Email" class="form-control" disabled />
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.NewEmail" class="form-label">Email mới</label>
                            <input asp-for="Input.NewEmail" class="form-control" />
                            <span asp-validation-for="Input.NewEmail" class="text-danger"></span>
                        </div>
                        
                        <button id="change-email-button" type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Thay đổi email
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
