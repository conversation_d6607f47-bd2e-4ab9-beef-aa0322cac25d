@model ReportsIndexViewModel
@{
    ViewData["Title"] = "Báo cáo Tổng quan";
}

<!-- Admin Reports Overview -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-chart-bar me-2"></i>
            Báo cáo Tổng quan
        </h2>
        <div>
            <a asp-action="UserReport" class="admin-btn admin-btn-info me-2">
                <i class="fas fa-users me-2"></i>
                Báo cáo Users
            </a>
            <a asp-action="CourseReport" class="admin-btn admin-btn-success me-2">
                <i class="fas fa-graduation-cap me-2"></i>
                Báo cáo Courses
            </a>
            <a asp-action="FinanceReport" class="admin-btn admin-btn-warning">
                <i class="fas fa-chart-line me-2"></i>
                Báo cáo Tài chính
            </a>
        </div>
    </div>

    <!-- Overall Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-primary">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Users</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalUsers</div>
                            <small class="text-muted">
                                @Model.VerifiedUsers xác thực / @Model.UnverifiedUsers chưa xác thực
                            </small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-success">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Courses</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalCourses</div>
                            <small class="text-muted">
                                @Model.PublishedCourses đã xuất bản / @Model.DraftCourses bản nháp
                            </small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-graduation-cap fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-info">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Enrollments</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalEnrollments</div>
                            <small class="text-muted">
                                @Model.ActiveEnrollments đang học / @Model.CompletedEnrollments hoàn thành
                            </small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-user-graduate fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-warning">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Doanh thu ròng</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.NetRevenue.ToString("N0") VNĐ</div>
                            <small class="text-muted">
                                @Model.TotalRevenue.ToString("N0") - @Model.TotalFees.ToString("N0") phí
                            </small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="row mb-4">
        <!-- Course Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thống kê Courses</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success mb-1">@Model.PublishedCourses</h4>
                                <small class="text-muted">Đã xuất bản</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning mb-1">@Model.DraftCourses</h4>
                                <small class="text-muted">Bản nháp</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info mb-1">@Model.FreeCourses</h4>
                                <small class="text-muted">Miễn phí</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary mb-1">@Model.PaidCourses</h4>
                                <small class="text-muted">Có phí</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enrollment Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thống kê Enrollments</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row text-center">
                        <div class="col-4 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success mb-1">@Model.ActiveEnrollments</h4>
                                <small class="text-muted">Đang học</small>
                            </div>
                        </div>
                        <div class="col-4 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary mb-1">@Model.CompletedEnrollments</h4>
                                <small class="text-muted">Hoàn thành</small>
                            </div>
                        </div>
                        <div class="col-4 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-danger mb-1">@Model.SuspendedEnrollments</h4>
                                <small class="text-muted">Tạm dừng</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Progress bars -->
                    @{
                        var totalEnrollments = Model.ActiveEnrollments + Model.CompletedEnrollments + Model.SuspendedEnrollments;
                        var activePercent = totalEnrollments > 0 ? (Model.ActiveEnrollments * 100.0 / totalEnrollments) : 0;
                        var completedPercent = totalEnrollments > 0 ? (Model.CompletedEnrollments * 100.0 / totalEnrollments) : 0;
                        var suspendedPercent = totalEnrollments > 0 ? (Model.SuspendedEnrollments * 100.0 / totalEnrollments) : 0;
                    }
                    
                    <div class="mt-3">
                        <div class="progress mb-2" style="height: 20px;">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: @activePercent%" 
                                 title="Đang học: @activePercent.ToString("F1")%">
                            </div>
                            <div class="progress-bar bg-primary" role="progressbar" 
                                 style="width: @completedPercent%" 
                                 title="Hoàn thành: @completedPercent.ToString("F1")%">
                            </div>
                            <div class="progress-bar bg-danger" role="progressbar" 
                                 style="width: @suspendedPercent%" 
                                 title="Tạm dừng: @suspendedPercent.ToString("F1")%">
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-success">Đang học: @activePercent.ToString("F1")%</small>
                            <small class="text-primary">Hoàn thành: @completedPercent.ToString("F1")%</small>
                            <small class="text-danger">Tạm dừng: @suspendedPercent.ToString("F1")%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Growth Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Xu hướng tăng trưởng (6 tháng gần nhất)</h5>
                </div>
                <div class="admin-card-body">
                    @if (Model.MonthlyGrowth.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tháng</th>
                                        <th>Users mới</th>
                                        <th>Courses mới</th>
                                        <th>Enrollments mới</th>
                                        <th>Doanh thu</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var growth in Model.MonthlyGrowth)
                                    {
                                        <tr>
                                            <td><strong>@growth.Month</strong></td>
                                            <td>
                                                <span class="badge bg-primary">@growth.NewUsers</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@growth.NewCourses</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@growth.NewEnrollments</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-warning">@growth.Revenue.ToString("N0") VNĐ</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Chưa có dữ liệu tăng trưởng</h6>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Top Categories and Courses -->
    <div class="row">
        <!-- Top Categories -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Top Categories (theo số courses)</h5>
                </div>
                <div class="admin-card-body">
                    @if (Model.TopCategories.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Courses</th>
                                        <th>Enrollments</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var category in Model.TopCategories.Take(5))
                                    {
                                        <tr>
                                            <td>
                                                <strong>@category.CategoryName</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@category.CourseCount</span>
                                                <small class="text-muted">(@category.PublishedCourses published)</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@category.TotalEnrollments</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Chưa có categories</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Top Courses -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Top Courses (theo enrollments)</h5>
                </div>
                <div class="admin-card-body">
                    @if (Model.TopCourses.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Course</th>
                                        <th>Enrollments</th>
                                        <th>Giá</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var course in Model.TopCourses.Take(5))
                                    {
                                        <tr>
                                            <td>
                                                <strong>@(course.CourseName.Length > 30 ? course.CourseName.Substring(0, 30) + "..." : course.CourseName)</strong>
                                                <br>
                                                <small class="text-muted">@course.CategoryName</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@course.EnrollmentCount</span>
                                            </td>
                                            <td>
                                                @if (course.Price > 0)
                                                {
                                                    <span class="fw-bold text-success">@course.Price.ToString("N0") VNĐ</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Miễn phí</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-graduation-cap fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Chưa có courses</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Báo cáo chi tiết</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row text-center">
                        <div class="col-md-4 mb-3">
                            <a asp-action="UserReport" class="btn btn-outline-primary btn-lg w-100">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <br>
                                <strong>Báo cáo Users</strong>
                                <br>
                                <small>Thống kê chi tiết về người dùng</small>
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a asp-action="CourseReport" class="btn btn-outline-success btn-lg w-100">
                                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                                <br>
                                <strong>Báo cáo Courses</strong>
                                <br>
                                <small>Thống kê chi tiết về khóa học</small>
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a asp-action="FinanceReport" class="btn btn-outline-warning btn-lg w-100">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <br>
                                <strong>Báo cáo Tài chính</strong>
                                <br>
                                <small>Thống kê chi tiết về doanh thu</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
