# 📊 Hướng dẫn Reset và Tạo lại Dữ liệu Mẫu

## 🎯 Mục đích
File `reset-sample-data.sql` được tạo để:
- X<PERSON>a toàn bộ dữ liệu cũ trong database
- Tạo lại dữ liệu mẫu hoàn chỉnh với encoding UTF-8 đúng
- Khắc phục lỗi hiển thị tiếng Việt bị sai

## 🚀 Cách sử dụng

### Phương pháp 1: Sử dụng Command Line
```bash
sqlcmd -S "DESKTOP-LJ825E1\SQLSERVER" -E -i reset-sample-data.sql
```

### Phương pháp 2: Sử dụng SQL Server Management Studio (SSMS)
1. Mở SSMS
2. Kết nối đến SQL Server instance: `DESKTOP-LJ825E1\SQLSERVER`
3. Mở file `reset-sample-data.sql`
4. Chọn database `ECourseWeb`
5. Nhấn F5 để thực thi

### Phương pháp 3: Sử dụng Azure Data Studio
1. Mở Azure Data Studio
2. Kết nối đến SQL Server
3. Mở file `reset-sample-data.sql`
4. Thực thi script

## 📋 Dữ liệu sẽ được tạo

### 🏷️ Categories (8 records)
- Lập trình Web
- Lập trình Mobile
- Cơ sở dữ liệu
- DevOps & Cloud
- AI & Machine Learning
- Blockchain & Web3
- Thiết kế UI/UX
- Bảo mật thông tin

### 📚 Courses (12 records)
- **Web Development:**
  - Khóa học ASP.NET Core từ cơ bản đến nâng cao
  - React.js - Xây dựng ứng dụng web hiện đại
  - Vue.js - Framework JavaScript linh hoạt

- **Mobile Development:**
  - Flutter - Phát triển ứng dụng di động đa nền tảng
  - React Native - Ứng dụng mobile với JavaScript

- **Database:**
  - SQL Server - Quản trị cơ sở dữ liệu chuyên nghiệp
  - MongoDB - Cơ sở dữ liệu NoSQL hiện đại

- **DevOps & Cloud:**
  - Docker & Kubernetes - DevOps thực chiến
  - AWS Cloud Practitioner - Điện toán đám mây

- **AI & Machine Learning:**
  - Python Machine Learning cơ bản
  - Deep Learning với TensorFlow

- **Blockchain:**
  - Blockchain & Smart Contracts với Solidity

### 📖 Chapters (19 records)
Mỗi khóa học có 3-5 chapters với nội dung chi tiết

### 🎥 Lessons (9 records)
Các bài học mẫu với video URL và thời lượng

### ❓ Questions & Answers (4 + 4 records)
Câu hỏi trắc nghiệm và tự luận với đáp án

### 👥 Enrollments (8 records)
Dữ liệu đăng ký khóa học của học viên

### 📈 Progress Tracking
- **LessonProgresses:** 8 records
- **Certificates:** 2 records

### 💬 Comments (5 records)
Đánh giá và nhận xét của học viên

### 💳 Payments (6 records)
Lịch sử thanh toán với các phương thức khác nhau

### 🎫 Discounts (4 records)
Mã giảm giá và khuyến mãi

### 💰 Finances (12 records)
Báo cáo tài chính theo tháng năm 2024

## ⚠️ Lưu ý quan trọng

1. **Backup dữ liệu:** Script sẽ XÓA TOÀN BỘ dữ liệu hiện có. Hãy backup trước khi chạy!

2. **Encoding:** Tất cả text tiếng Việt đều sử dụng prefix `N` để đảm bảo UTF-8 encoding đúng

3. **Foreign Keys:** Script xóa dữ liệu theo đúng thứ tự để tránh lỗi foreign key constraint

4. **Identity Seeds:** Tất cả identity columns đều được reset về 0

## 🔍 Kiểm tra sau khi chạy

Sau khi chạy script thành công, bạn có thể kiểm tra:

```sql
-- Kiểm tra số lượng records
SELECT 'Categories' as TableName, COUNT(*) as RecordCount FROM Categories
UNION ALL
SELECT 'Courses', COUNT(*) FROM Courses
UNION ALL
SELECT 'Chapters', COUNT(*) FROM Chapters
UNION ALL
SELECT 'Lessons', COUNT(*) FROM Lessons;

-- Kiểm tra encoding tiếng Việt
SELECT TOP 3 Title, Description FROM Courses;
```

## 🐛 Troubleshooting

### Lỗi Permission
```
Msg 229, Level 14, State 5: The DELETE permission was denied
```
**Giải pháp:** Đảm bảo user có quyền DELETE trên database

### Lỗi Foreign Key
```
Msg 547, Level 16, State 0: The DELETE statement conflicted with the REFERENCE constraint
```
**Giải pháp:** Script đã xử lý thứ tự xóa, nếu vẫn lỗi hãy kiểm tra có data nào đang được reference không

### Lỗi Encoding vẫn sai
**Giải pháp:** 
1. Kiểm tra collation của database
2. Đảm bảo file SQL được save với UTF-8 encoding
3. Restart ứng dụng web sau khi update data

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Kết nối SQL Server
2. Quyền truy cập database
3. Database `ECourseWeb` đã tồn tại
4. Không có ứng dụng nào đang sử dụng database

---
**Tạo bởi:** ELearning Website Development Team  
**Cập nhật:** 2024-12-22
