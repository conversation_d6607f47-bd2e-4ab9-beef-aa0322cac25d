@model ELearningWebsite.Areas.Admin.ViewModels.ChapterStatisticsViewModel
@{
    ViewData["Title"] = "Thống kê Chương";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-chart-bar text-info"></i>
                Thống kê Chương
            </h1>
            <p class="admin-page-subtitle">Báo cáo và phân tích dữ liệu chương học</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> Danh sách
            </a>
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus"></i> Tạo mới
            </a>
            <button onclick="window.print()" class="btn btn-outline-primary">
                <i class="fas fa-print"></i> In báo cáo
            </button>
        </div>
    </div>
</div>

<!-- Overview Cards -->
<div class="admin-summary-cards">
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-primary">
                    <i class="fas fa-list-ol"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.TotalChapters</h3>
                    <p>Tổng chương</p>
                    <small class="text-muted">Tất cả chương đã tạo</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.ActiveChapters</h3>
                    <p>Đang hoạt động</p>
                    <small class="text-muted">Chương hiển thị cho học viên</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-warning">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.InactiveChapters</h3>
                    <p>Không hoạt động</p>
                    <small class="text-muted">Chương bị ẩn</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-info">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.TotalLessons</h3>
                    <p>Tổng bài học</p>
                    <small class="text-muted">Bài học trong tất cả chương</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Chapter Trend Chart -->
    <div class="col-lg-8">
        <div class="admin-chart-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-chart-line text-primary"></i>
                    Xu hướng tạo chương (30 ngày qua)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="chapterTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Status Distribution -->
    <div class="col-lg-4">
        <div class="admin-chart-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-pie-chart text-success"></i>
                    Phân bố trạng thái
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
                <div class="chart-legend mt-3">
                    @foreach (var status in Model.StatusDistribution)
                    {
                        <div class="legend-item">
                            <span class="legend-color bg-@(status.Status == "Active" ? "success" : "secondary")"></span>
                            <span>@status.Status (@status.Count)</span>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Courses -->
    <div class="col-lg-6">
        <div class="admin-table-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-trophy text-warning"></i>
                    Top khóa học có nhiều chương nhất
                </h5>
            </div>
            <div class="card-body">
                @if (Model.TopCourses.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Khóa học</th>
                                    <th>Chương</th>
                                    <th>Bài học</th>
                                    <th>Giá</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var course in Model.TopCourses)
                                {
                                    <tr>
                                        <td>
                                            <div class="course-info">
                                                <strong>@course.CourseName</strong>
                                                <br>
                                                <small class="text-muted">ID: @course.CourseId</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">@course.ChapterCount</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@course.LessonCount</span>
                                        </td>
                                        <td>
                                            <span class="text-success">@course.CoursePrice.ToString("N0") VNĐ</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Chưa có dữ liệu</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Recent Chapters -->
    <div class="col-lg-6">
        <div class="admin-table-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-clock text-info"></i>
                    Chương mới nhất
                </h5>
            </div>
            <div class="card-body">
                @if (Model.RecentChapters.Any())
                {
                    <div class="recent-chapters">
                        @foreach (var chapter in Model.RecentChapters)
                        {
                            <div class="recent-item">
                                <div class="recent-icon">
                                    <i class="fas fa-list-ol text-primary"></i>
                                </div>
                                <div class="recent-content">
                                    <div class="recent-title">
                                        <a asp-action="Details" asp-route-id="@chapter.Id" class="text-decoration-none">
                                            @chapter.Name
                                        </a>
                                    </div>
                                    <div class="recent-details">
                                        <small class="text-muted">
                                            @chapter.CourseName
                                        </small>
                                        <span class="badge bg-@(chapter.Status == "Active" ? "success" : "secondary") ms-2">
                                            @chapter.Status
                                        </span>
                                    </div>
                                    <div class="recent-time">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i>
                                            @chapter.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                        </small>
                                        <small class="text-info ms-2">@chapter.CreatedByName</small>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-list-ol fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Chưa có chương nào</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row">
    <div class="col-lg-12">
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle text-primary"></i>
                    Thông tin chi tiết
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-primary">@Model.ChaptersThisMonth</div>
                            <div class="stat-label">Chương tháng này</div>
                            <small class="text-muted">Số chương được tạo trong tháng</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-success">@Model.ChaptersThisYear</div>
                            <div class="stat-label">Chương năm này</div>
                            <small class="text-muted">Số chương được tạo trong năm</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-info">@Model.AverageLessonsPerChapter.ToString("F1")</div>
                            <div class="stat-label">TB bài học/chương</div>
                            <small class="text-muted">Trung bình bài học mỗi chương</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-warning">@(Model.TotalChapters > 0 ? (Model.ActiveChapters * 100.0 / Model.TotalChapters).ToString("F1") : "0")%</div>
                            <div class="stat-label">Tỷ lệ hoạt động</div>
                            <small class="text-muted">Phần trăm chương đang hoạt động</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Chapter Trend Chart
const trendCtx = document.getElementById('chapterTrendChart').getContext('2d');
const trendData = @Html.Raw(Json.Serialize(Model.TrendData.Select(t => new { 
    date = t.Date.ToString("dd/MM"), 
    count = t.Count 
})));

new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: trendData.map(d => d.date),
        datasets: [{
            label: 'Chương được tạo',
            data: trendData.map(d => d.count),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusData = @Html.Raw(Json.Serialize(Model.StatusDistribution));

new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: statusData.map(s => s.status),
        datasets: [{
            data: statusData.map(s => s.count),
            backgroundColor: statusData.map(s => s.status === 'Active' ? '#28a745' : '#6c757d'),
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<style>
.recent-chapters {
    max-height: 400px;
    overflow-y: auto;
}

.recent-item {
    display: flex;
    align-items-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-icon {
    margin-right: 0.75rem;
    margin-top: 0.25rem;
}

.recent-content {
    flex: 1;
}

.recent-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.recent-details {
    margin-bottom: 0.25rem;
}

.recent-time {
    font-size: 0.8rem;
}

.detail-stat {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.course-info strong {
    font-size: 0.9rem;
}
</style>
