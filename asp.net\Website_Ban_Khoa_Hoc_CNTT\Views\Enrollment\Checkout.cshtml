@model CheckoutViewModel
@{
    ViewData["Title"] = "Thanh toán - " + Model.Course.Title;
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Courses", "Home")">Khóa học</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("CourseDetail", "Home", new { id = Model.Course.Id })">@Model.Course.Title</a></li>
                    <li class="breadcrumb-item active">Thanh toán</li>
                </ol>
            </nav>

            <div class="row">
                <!-- Course Info -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Thông tin đơn hàng</h4>
                        </div>
                        <div class="card-body">
                            <div class="d-flex">
                                <img src="@Model.Course.Thumbnail" alt="@Model.Course.Title" 
                                     class="rounded me-3" style="width: 120px; height: 80px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h5 class="mb-2">@Model.Course.Title</h5>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-tag me-1"></i>@Model.Course.Category.Name
                                    </p>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-clock me-1"></i>
                                        Thời lượng: @(Model.Course.Duration?.ToString(@"hh\:mm") ?? "Đang cập nhật")
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Discount Code -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-percent me-2"></i>Mã giảm giá</h5>
                        </div>
                        <div class="card-body">
                            @if (Model.AppliedDiscount != null)
                            {
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Đã áp dụng mã giảm giá: <strong>@Model.AppliedDiscount.Code</strong>
                                    (Giảm @Model.AppliedDiscount.DiscountPer%)
                                </div>
                            }
                            else
                            {
                                <form id="discountForm">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="discountCode" 
                                               placeholder="Nhập mã giảm giá (nếu có)">
                                        <button type="button" class="btn btn-outline-primary" onclick="applyDiscount()">
                                            Áp dụng
                                        </button>
                                    </div>
                                </form>
                            }
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Phương thức thanh toán</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="payment-method p-3 border rounded">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="paymentMethod" 
                                                   id="bankTransfer" value="bank_transfer" checked>
                                            <label class="form-check-label" for="bankTransfer">
                                                <i class="fas fa-university me-2 text-primary"></i>
                                                <strong>Chuyển khoản ngân hàng</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted d-block mt-2">
                                            Chuyển khoản trực tiếp qua ngân hàng
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="payment-method p-3 border rounded">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="paymentMethod" 
                                                   id="momo" value="momo">
                                            <label class="form-check-label" for="momo">
                                                <i class="fas fa-mobile-alt me-2 text-danger"></i>
                                                <strong>Ví MoMo</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted d-block mt-2">
                                            Thanh toán qua ví điện tử MoMo
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Bank Transfer Info -->
                            <div id="bankInfo" class="mt-3">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Thông tin chuyển khoản:</h6>
                                    <p class="mb-2"><strong>Ngân hàng:</strong> Vietcombank</p>
                                    <p class="mb-2"><strong>Số tài khoản:</strong> **********</p>
                                    <p class="mb-2"><strong>Chủ tài khoản:</strong> WEBSITE BAN KHOA HOC CNTT</p>
                                    <p class="mb-0"><strong>Nội dung:</strong> THANHTOAN @Model.Course.Id @User.Identity!.Name</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-md-4">
                    <div class="card sticky-top" style="top: 100px;">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Tóm tắt đơn hàng</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Giá gốc:</span>
                                <span>@Model.OriginalPrice.ToString("N0") VNĐ</span>
                            </div>
                            
                            @if (Model.DiscountAmount > 0)
                            {
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>Giảm giá:</span>
                                    <span>-@Model.DiscountAmount.ToString("N0") VNĐ</span>
                                </div>
                            }
                            
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Tổng cộng:</strong>
                                <strong class="text-primary">@Model.FinalPrice.ToString("N0") VNĐ</strong>
                            </div>

                            <form asp-action="ProcessPayment" method="post">
                                <input type="hidden" name="CourseId" value="@Model.Course.Id" />
                                <input type="hidden" name="OriginalPrice" value="@Model.OriginalPrice" />
                                <input type="hidden" name="FinalPrice" value="@Model.FinalPrice" />
                                <input type="hidden" name="DiscountAmount" value="@Model.DiscountAmount" />
                                <input type="hidden" name="DiscountCode" value="@Model.AppliedDiscount?.Code" />
                                
                                <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                    <i class="fas fa-lock me-2"></i>Xác nhận thanh toán
                                </button>
                            </form>

                            <div class="text-center">
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Thanh toán được bảo mật 100%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .payment-method {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .payment-method:hover {
        border-color: #0d6efd !important;
        background-color: #f8f9fa;
    }
    
    .payment-method input[type="radio"]:checked + label {
        color: #0d6efd;
        font-weight: 600;
    }
</style>

<script>
    function applyDiscount() {
        const discountCode = document.getElementById('discountCode').value.trim();
        if (!discountCode) {
            showToast('Vui lòng nhập mã giảm giá!', 'warning');
            return;
        }

        // Redirect với discount code
        window.location.href = '@Url.Action("Checkout", "Enrollment", new { id = Model.Course.Id })' + '&discountCode=' + encodeURIComponent(discountCode);
    }

    // Handle payment method selection
    document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const bankInfo = document.getElementById('bankInfo');
            if (this.value === 'bank_transfer') {
                bankInfo.style.display = 'block';
            } else {
                bankInfo.style.display = 'none';
            }
        });
    });

    // Click on payment method div to select radio
    document.querySelectorAll('.payment-method').forEach(div => {
        div.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            radio.dispatchEvent(new Event('change'));
        });
    });

    function showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
</script>
