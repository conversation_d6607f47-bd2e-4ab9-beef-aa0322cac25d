# Website B<PERSON> Khóa Học CNTT

Một website bán khóa học công nghệ thông tin được xây dựng bằng ASP.NET Core 8.0 với giao diện hiện đại, trẻ trung và đẹp mắt.

## Tính năng chính

### Giao diện người dùng (Shop)
- **Trang chủ**: <PERSON><PERSON><PERSON> thị khóa học nổi bật, mới nhất và thống kê
- **Danh sách khóa học**: Xem tất cả khóa học với tính năng tìm kiếm và lọc
- **Chi tiết khóa học**: <PERSON>em thông tin chi tiết, nội dung và đánh giá
- **Đăng ký/Đăng nhập**: Quản lý tài khoản người dùng
- **Responsive Design**: Tương thích với mọi thiết bị

### Giao diện quản trị (Admin)
- **Dashboard**: <PERSON><PERSON><PERSON><PERSON> kê tổng quan về khóa họ<PERSON>, <PERSON><PERSON><PERSON> v<PERSON>, do<PERSON><PERSON> thu
- **Quản lý khóa học**: Thêm, sửa, xóa khóa học và nội dung
- **Quản lý người dùng**: Phân quyền và quản lý tài khoản
- **Báo cáo**: Thống kê chi tiết về hoạt động website

## Công nghệ sử dụng

- **Backend**: ASP.NET Core 8.0, Entity Framework Core
- **Frontend**: Bootstrap 5, Font Awesome, AOS Animation
- **Database**: SQL Server (sử dụng database có sẵn)
- **Authentication**: ASP.NET Core Identity
- **UI Libraries**: CDN (Bootstrap, Font Awesome, Google Fonts)

## Cấu trúc Database

Website sử dụng database có sẵn với các bảng:
- `Categories`: Danh mục khóa học
- `Courses`: Thông tin khóa học
- `Chapters`: Chương học
- `Enrollments`: Đăng ký khóa học
- `LessonProgresses`: Tiến độ học tập
- `Comments`: Bình luận
- `Certificates`: Chứng chỉ
- `Discounts`: Mã giảm giá
- `Finances`: Quản lý tài chính

## Cài đặt và chạy

### Yêu cầu hệ thống
- .NET 8.0 SDK
- SQL Server
- Visual Studio 2022 hoặc VS Code

### Hướng dẫn cài đặt

1. **Clone project**:
   ```bash
   git clone [repository-url]
   cd Website_Ban_Khoa_Hoc_CNTT
   ```

2. **Cấu hình database**:
   - Mở file `appsettings.json`
   - Cập nhật connection string để kết nối đến SQL Server của bạn:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=YOUR_SERVER;Database=ECourse;User Id=YOUR_USER;Password=YOUR_PASSWORD;TrustServerCertificate=true;"
     }
   }
   ```

3. **Chạy ứng dụng**:
   ```bash
   # Cách 1: Sử dụng script
   run.bat
   
   # Cách 2: Sử dụng dotnet CLI
   dotnet restore
   dotnet build
   dotnet run
   ```

4. **Truy cập website**:
   - **Trang chủ**: http://localhost:5000 hoặc https://localhost:5001
   - **Admin**: https://localhost:5001/Admin

### Tài khoản mặc định

Sau khi chạy lần đầu, hệ thống sẽ tự động tạo các tài khoản:

- **Admin**: 
  - Email: <EMAIL>
  - Password: Admin@123

- **Instructor**:
  - Email: <EMAIL>  
  - Password: Instructor@123

- **Student**:
  - Email: <EMAIL>
  - Password: Student@123

## Cấu trúc thư mục

```
Website_Ban_Khoa_Hoc_CNTT/
├── Areas/
│   └── Admin/              # Khu vực quản trị
├── Controllers/            # Controllers cho shop
├── Data/                   # DbContext và cấu hình database
├── Models/                 # Entity models
├── Services/               # Business logic services
├── Views/                  # Razor views
│   ├── Home/              # Views cho trang chủ
│   ├── Shared/            # Layout và partial views
│   └── ...
├── wwwroot/               # Static files
├── appsettings.json       # Cấu hình ứng dụng
├── Program.cs             # Entry point
└── README.md              # Tài liệu này
```

## Tính năng đang phát triển

- Hệ thống thanh toán trực tuyến
- Quản lý bài học và video
- Hệ thống quiz và kiểm tra
- Chat support trực tuyến
- Mobile app

## Liên hệ

Nếu có thắc mắc hoặc cần hỗ trợ, vui lòng liên hệ:
- Email: <EMAIL>
- Website: https://elearning-cntt.com

## License

Dự án này được phát triển cho mục đích học tập và thương mại.
