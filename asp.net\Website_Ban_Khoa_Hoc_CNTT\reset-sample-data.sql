-- =============================================
-- Script: Reset và tạo lại toàn bộ dữ liệu mẫu
-- Database: ECourseWeb
-- Encoding: UTF-8 với prefix N cho tiếng Việt
-- =============================================

USE ECourseWeb;
GO

-- =============================================
-- 1. XÓA TOÀN BỘ DỮ LIỆU CŨ (THEO THỨ TỰ ĐÚNG)
-- =============================================
PRINT 'Đang xóa dữ liệu cũ...';

-- X<PERSON>a theo thứ tự từ bảng con đến bảng cha để tránh lỗi foreign key
DELETE FROM Answers;
DELETE FROM Questions;
DELETE FROM LessonProgresses;
DELETE FROM Lessons;
DELETE FROM Comments;
DELETE FROM Certificates;
DELETE FROM Payments;
DELETE FROM Enrollments;
DELETE FROM Chapters;
DELETE FROM Courses;
DELETE FROM Categories;
DELETE FROM Discounts;
DELETE FROM Finances;

-- Reset Identity Seeds cho tất cả bảng có IDENTITY
DBCC CHECKIDENT ('Categories', RESEED, 0);
DBCC CHECKIDENT ('Courses', RESEED, 0);
DBCC CHECKIDENT ('Chapters', RESEED, 0);
DBCC CHECKIDENT ('Lessons', RESEED, 0);
DBCC CHECKIDENT ('Questions', RESEED, 0);
DBCC CHECKIDENT ('Answers', RESEED, 0);
DBCC CHECKIDENT ('Enrollments', RESEED, 0);
DBCC CHECKIDENT ('LessonProgresses', RESEED, 0);
DBCC CHECKIDENT ('Certificates', RESEED, 0);
DBCC CHECKIDENT ('Comments', RESEED, 0);
DBCC CHECKIDENT ('Payments', RESEED, 0);
DBCC CHECKIDENT ('Discounts', RESEED, 0);
DBCC CHECKIDENT ('Finances', RESEED, 0);

PRINT 'Đã xóa xong dữ liệu cũ.';
GO

-- =============================================
-- 2. THÊM DỮ LIỆU MẪU - CATEGORIES
-- =============================================
PRINT 'Đang thêm Categories...';

INSERT INTO Categories (Name, Description, Status) VALUES
(N'Lập trình Web', N'Các khóa học về phát triển web frontend và backend', 1),
(N'Lập trình Mobile', N'Các khóa học về phát triển ứng dụng di động', 1),
(N'Cơ sở dữ liệu', N'Các khóa học về quản trị và phát triển cơ sở dữ liệu', 1),
(N'DevOps & Cloud', N'Các khóa học về DevOps và điện toán đám mây', 1),
(N'AI & Machine Learning', N'Các khóa học về trí tuệ nhân tạo và học máy', 1),
(N'Blockchain & Web3', N'Các khóa học về blockchain và công nghệ Web3', 1),
(N'Thiết kế UI/UX', N'Các khóa học về thiết kế giao diện người dùng', 1),
(N'Bảo mật thông tin', N'Các khóa học về an ninh mạng và bảo mật', 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Categories.';
GO

-- =============================================
-- 3. THÊM DỮ LIỆU MẪU - COURSES
-- =============================================
PRINT 'Đang thêm Courses...';

INSERT INTO Courses (Title, CategoryId, Price, Thumbnail, Description, Status, PreviewVideo, CreatedAt, CreateBy, Duration, LimitDay) VALUES
-- Web Development Courses
(N'Khóa học ASP.NET Core từ cơ bản đến nâng cao', 1, 299000, 'https://via.placeholder.com/400x300/6366f1/ffffff?text=ASP.NET+Core', N'Học ASP.NET Core từ cơ bản đến nâng cao với các dự án thực tế. Khóa học bao gồm MVC, Web API, Entity Framework Core, Authentication và Authorization.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 40, 365),
(N'React.js - Xây dựng ứng dụng web hiện đại', 1, 399000, 'https://via.placeholder.com/400x300/61dafb/000000?text=React.js', N'Khóa học React.js toàn diện với hooks, context, Redux và các thư viện phổ biến. Xây dựng ứng dụng web single-page hiện đại.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 35, 365),
(N'Vue.js - Framework JavaScript linh hoạt', 1, 349000, 'https://via.placeholder.com/400x300/4fc08d/ffffff?text=Vue.js', N'Học Vue.js từ cơ bản đến nâng cao, bao gồm Vuex, Vue Router và Nuxt.js cho phát triển ứng dụng web hiệu quả.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 30, 365),

-- Mobile Development Courses
(N'Flutter - Phát triển ứng dụng di động đa nền tảng', 2, 499000, 'https://via.placeholder.com/400x300/02569b/ffffff?text=Flutter', N'Học Flutter để tạo ứng dụng iOS và Android với một codebase. Bao gồm Dart, widgets, state management và deployment.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 45, 365),
(N'React Native - Ứng dụng mobile với JavaScript', 2, 449000, 'https://via.placeholder.com/400x300/61dafb/000000?text=React+Native', N'Phát triển ứng dụng mobile đa nền tảng với React Native. Học navigation, API integration và publishing app.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 40, 365),

-- Database Courses
(N'SQL Server - Quản trị cơ sở dữ liệu chuyên nghiệp', 3, 349000, 'https://via.placeholder.com/400x300/cc2927/ffffff?text=SQL+Server', N'Khóa học SQL Server từ cơ bản đến nâng cao cho DBA. Bao gồm T-SQL, stored procedures, indexing và performance tuning.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 50, 365),
(N'MongoDB - Cơ sở dữ liệu NoSQL hiện đại', 3, 299000, 'https://via.placeholder.com/400x300/47a248/ffffff?text=MongoDB', N'Học MongoDB từ cơ bản đến nâng cao. Thiết kế schema, aggregation, indexing và integration với Node.js.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 25, 365),

-- DevOps & Cloud Courses
(N'Docker & Kubernetes - DevOps thực chiến', 4, 599000, 'https://via.placeholder.com/400x300/2496ed/ffffff?text=Docker+K8s', N'Học Docker và Kubernetes để triển khai ứng dụng hiệu quả. Container orchestration, CI/CD và monitoring.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 60, 365),
(N'AWS Cloud Practitioner - Điện toán đám mây', 4, 549000, 'https://via.placeholder.com/400x300/ff9900/ffffff?text=AWS', N'Khóa học AWS từ cơ bản đến chứng chỉ Cloud Practitioner. EC2, S3, RDS, Lambda và các services cốt lõi.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 35, 365),

-- AI & ML Courses
(N'Python Machine Learning cơ bản', 5, 449000, 'https://via.placeholder.com/400x300/3776ab/ffffff?text=Python+ML', N'Khóa học Machine Learning với Python và các thư viện phổ biến như scikit-learn, pandas, numpy.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 55, 365),
(N'Deep Learning với TensorFlow', 5, 699000, 'https://via.placeholder.com/400x300/ff6f00/ffffff?text=TensorFlow', N'Học Deep Learning với TensorFlow và Keras. Neural networks, CNN, RNN và các ứng dụng thực tế.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 70, 365),

-- Blockchain Courses
(N'Blockchain & Smart Contracts với Solidity', 6, 799000, 'https://via.placeholder.com/400x300/627eea/ffffff?text=Blockchain', N'Học phát triển blockchain và smart contracts với Solidity. Ethereum, Web3.js và DApp development.', 'Published', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', GETDATE(), 1, 50, 365);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Courses.';
GO

-- =============================================
-- 4. THÊM DỮ LIỆU MẪU - CHAPTERS
-- =============================================
PRINT 'Đang thêm Chapters...';

INSERT INTO Chapters (CourseId, Name, Description, Status, CreatedAt, CreateBy) VALUES
-- Chapters cho ASP.NET Core (CourseId = 1)
(1, N'Giới thiệu ASP.NET Core', N'Tổng quan về ASP.NET Core và cài đặt môi trường phát triển', 'Published', GETDATE(), 1),
(1, N'MVC Pattern và Routing', N'Hiểu về mô hình MVC và cách định tuyến trong ASP.NET Core', 'Published', GETDATE(), 1),
(1, N'Entity Framework Core', N'Làm việc với cơ sở dữ liệu sử dụng EF Core ORM', 'Published', GETDATE(), 1),
(1, N'Authentication & Authorization', N'Xác thực và phân quyền trong ASP.NET Core', 'Published', GETDATE(), 1),
(1, N'Web API Development', N'Xây dựng RESTful API với ASP.NET Core', 'Published', GETDATE(), 1),

-- Chapters cho React.js (CourseId = 2)
(2, N'Giới thiệu React.js', N'Tổng quan về React.js và JSX syntax', 'Published', GETDATE(), 1),
(2, N'Components và Props', N'Tạo và sử dụng components trong React', 'Published', GETDATE(), 1),
(2, N'State và Hooks', N'Quản lý state với useState và useEffect hooks', 'Published', GETDATE(), 1),
(2, N'Event Handling', N'Xử lý sự kiện trong React applications', 'Published', GETDATE(), 1),
(2, N'React Router', N'Navigation và routing trong React SPA', 'Published', GETDATE(), 1),

-- Chapters cho Flutter (CourseId = 4)
(4, N'Dart Programming Language', N'Học ngôn ngữ Dart cơ bản cho Flutter', 'Published', GETDATE(), 1),
(4, N'Flutter Widgets', N'Các widgets cơ bản và layout trong Flutter', 'Published', GETDATE(), 1),
(4, N'State Management', N'Quản lý state với Provider và Bloc pattern', 'Published', GETDATE(), 1),
(4, N'Navigation & Routing', N'Điều hướng giữa các màn hình trong Flutter', 'Published', GETDATE(), 1),
(4, N'API Integration', N'Tích hợp API và xử lý dữ liệu từ server', 'Published', GETDATE(), 1),

-- Chapters cho SQL Server (CourseId = 6)
(6, N'SQL Server Fundamentals', N'Cơ bản về SQL Server và T-SQL', 'Published', GETDATE(), 1),
(6, N'Database Design', N'Thiết kế cơ sở dữ liệu và normalization', 'Published', GETDATE(), 1),
(6, N'Stored Procedures & Functions', N'Tạo và sử dụng stored procedures, functions', 'Published', GETDATE(), 1),
(6, N'Indexing & Performance', N'Tối ưu hóa hiệu suất với indexing', 'Published', GETDATE(), 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Chapters.';
GO

-- =============================================
-- 5. THÊM DỮ LIỆU MẪU - LESSONS
-- =============================================
PRINT 'Đang thêm Lessons...';

INSERT INTO Lessons (ChapterId, Name, Content, VideoUrl, Duration, Status, CreatedAt, CreateBy) VALUES
-- Lessons cho Chapter 1 (Giới thiệu ASP.NET Core)
(1, N'Cài đặt .NET SDK', N'Hướng dẫn cài đặt .NET SDK và Visual Studio', 'https://www.youtube.com/watch?v=example1', 15, 'Published', GETDATE(), 1),
(1, N'Tạo project đầu tiên', N'Tạo và chạy ứng dụng ASP.NET Core đầu tiên', 'https://www.youtube.com/watch?v=example2', 20, 'Published', GETDATE(), 1),
(1, N'Cấu trúc project', N'Hiểu về cấu trúc thư mục và files trong project', 'https://www.youtube.com/watch?v=example3', 18, 'Published', GETDATE(), 1),

-- Lessons cho Chapter 2 (MVC Pattern)
(2, N'Hiểu về MVC Pattern', N'Tìm hiểu về mô hình Model-View-Controller', 'https://www.youtube.com/watch?v=example4', 25, 'Published', GETDATE(), 1),
(2, N'Controllers và Actions', N'Tạo và sử dụng Controllers trong ASP.NET Core', 'https://www.youtube.com/watch?v=example5', 30, 'Published', GETDATE(), 1),
(2, N'Views và Razor Pages', N'Làm việc với Views và Razor syntax', 'https://www.youtube.com/watch?v=example6', 35, 'Published', GETDATE(), 1),

-- Lessons cho Chapter Flutter Widgets (ChapterId = 8 - Flutter Widgets)
(8, N'StatelessWidget vs StatefulWidget', N'Phân biệt và sử dụng hai loại widget cơ bản', 'https://www.youtube.com/watch?v=flutter1', 22, 'Published', GETDATE(), 1),
(8, N'Layout Widgets', N'Container, Row, Column và các layout widgets', 'https://www.youtube.com/watch?v=flutter2', 28, 'Published', GETDATE(), 1),
(8, N'Material Design Widgets', N'AppBar, FloatingActionButton, Card widgets', 'https://www.youtube.com/watch?v=flutter3', 25, 'Published', GETDATE(), 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Lessons.';
GO

-- =============================================
-- 6. THÊM DỮ LIỆU MẪU - ENROLLMENTS
-- =============================================
PRINT 'Đang thêm Enrollments...';

INSERT INTO Enrollments (UserId, CourseId, EnrollmentDate, Progress, Status) VALUES
(1, 1, DATEADD(day, -30, GETDATE()), 75.5, 1),
(1, 2, DATEADD(day, -25, GETDATE()), 45.0, 1),
(1, 4, DATEADD(day, -20, GETDATE()), 30.0, 1),
(2, 1, DATEADD(day, -15, GETDATE()), 90.0, 1),
(2, 6, DATEADD(day, -10, GETDATE()), 60.0, 1),
(3, 2, DATEADD(day, -8, GETDATE()), 25.0, 1),
(3, 4, DATEADD(day, -5, GETDATE()), 15.0, 1),
(4, 1, DATEADD(day, -3, GETDATE()), 10.0, 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Enrollments.';
GO

-- =============================================
-- 7. THÊM DỮ LIỆU MẪU - QUESTIONS
-- =============================================
PRINT 'Đang thêm Questions...';

INSERT INTO Questions (LessonId, Content, Type, Status, CreatedAt, CreateBy) VALUES
(1, N'Câu hỏi: .NET SDK là gì và tại sao cần cài đặt nó?', 'Essay', 'Published', GETDATE(), 1),
(2, N'Câu hỏi: Hãy giải thích cấu trúc cơ bản của một ASP.NET Core project?', 'Essay', 'Published', GETDATE(), 1),
(4, N'Câu hỏi: MVC Pattern có những thành phần nào?', 'MultipleChoice', 'Published', GETDATE(), 1),
(5, N'Câu hỏi: Controller trong ASP.NET Core có vai trò gì?', 'Essay', 'Published', GETDATE(), 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Questions.';
GO

-- =============================================
-- 8. THÊM DỮ LIỆU MẪU - ANSWERS
-- =============================================
PRINT 'Đang thêm Answers...';

INSERT INTO Answers (QuestionId, Content, IsCorrect, CreatedAt, CreateBy) VALUES
-- Answers cho câu hỏi MVC Pattern
(3, N'Model, View, Controller', 1, GETDATE(), 1),
(3, N'Model, View, Component', 0, GETDATE(), 1),
(3, N'Module, View, Controller', 0, GETDATE(), 1),
(3, N'Model, Variable, Controller', 0, GETDATE(), 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Answers.';
GO

-- =============================================
-- 9. THÊM DỮ LIỆU MẪU - LESSON PROGRESSES
-- =============================================
PRINT 'Đang thêm LessonProgresses...';

INSERT INTO LessonProgresses (UserId, LessonId, IsCompleted, CompletedAt, WatchTime) VALUES
(1, 1, 1, DATEADD(day, -29, GETDATE()), 15),
(1, 2, 1, DATEADD(day, -28, GETDATE()), 20),
(1, 3, 1, DATEADD(day, -27, GETDATE()), 18),
(1, 4, 0, NULL, 12),
(2, 1, 1, DATEADD(day, -14, GETDATE()), 15),
(2, 2, 1, DATEADD(day, -13, GETDATE()), 20),
(2, 3, 1, DATEADD(day, -12, GETDATE()), 18),
(2, 4, 1, DATEADD(day, -11, GETDATE()), 25);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' LessonProgresses.';
GO

-- =============================================
-- 10. THÊM DỮ LIỆU MẪU - CERTIFICATES
-- =============================================
PRINT 'Đang thêm Certificates...';

INSERT INTO Certificates (UserId, CourseId, CertificateCode, IssuedAt, ExpiryDate) VALUES
(2, 1, 'CERT-ASPNET-001', DATEADD(day, -5, GETDATE()), DATEADD(year, 2, GETDATE())),
(1, 1, 'CERT-ASPNET-002', DATEADD(day, -2, GETDATE()), DATEADD(year, 2, GETDATE()));

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Certificates.';
GO

-- =============================================
-- 11. THÊM DỮ LIỆU MẪU - COMMENTS
-- =============================================
PRINT 'Đang thêm Comments...';

INSERT INTO Comments (UserId, CourseId, Content, Rating, Status, CreatedAt) VALUES
(1, 1, N'Khóa học rất hay và chi tiết. Giảng viên giải thích dễ hiểu, phù hợp cho người mới bắt đầu.', 5, 'Approved', DATEADD(day, -10, GETDATE())),
(2, 1, N'Nội dung cập nhật, có nhiều ví dụ thực tế. Tôi đã áp dụng được vào công việc ngay.', 5, 'Approved', DATEADD(day, -8, GETDATE())),
(3, 2, N'React.js được giảng dạy từ cơ bản đến nâng cao một cách logic và dễ theo dõi.', 4, 'Approved', DATEADD(day, -5, GETDATE())),
(1, 4, N'Flutter thật sự mạnh mẽ. Sau khóa học tôi đã tạo được app đầu tiên của mình.', 5, 'Approved', DATEADD(day, -3, GETDATE())),
(4, 1, N'Khóa học tốt nhưng cần thêm nhiều bài tập thực hành hơn.', 4, 'Approved', DATEADD(day, -1, GETDATE()));

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Comments.';
GO

-- =============================================
-- 12. THÊM DỮ LIỆU MẪU - PAYMENTS
-- =============================================
PRINT 'Đang thêm Payments...';

INSERT INTO Payments (UserId, CourseId, Amount, PaymentMethod, Status, TransactionId, PaymentDate) VALUES
(1, 1, 299000, N'VNPay', 'Completed', 'TXN001', DATEADD(day, -30, GETDATE())),
(1, 2, 399000, N'MoMo', 'Completed', 'TXN002', DATEADD(day, -25, GETDATE())),
(2, 1, 299000, N'Banking', 'Completed', 'TXN003', DATEADD(day, -15, GETDATE())),
(2, 6, 349000, N'VNPay', 'Completed', 'TXN004', DATEADD(day, -10, GETDATE())),
(3, 2, 399000, N'MoMo', 'Completed', 'TXN005', DATEADD(day, -8, GETDATE())),
(4, 1, 299000, N'Banking', 'Pending', 'TXN006', DATEADD(day, -3, GETDATE()));

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Payments.';
GO

-- =============================================
-- 13. THÊM DỮ LIỆU MẪU - DISCOUNTS
-- =============================================
PRINT 'Đang thêm Discounts...';

INSERT INTO Discounts (Code, Name, Description, DiscountType, DiscountValue, MinOrderValue, MaxDiscountAmount, StartDate, EndDate, UsageLimit, UsedCount, Status, CreatedAt, CreateBy) VALUES
(N'WELCOME2024', N'Chào mừng năm mới', N'Giảm giá 20% cho khách hàng mới trong tháng 1', 'Percentage', 20, 200000, 100000, '2024-01-01', '2024-01-31', 100, 25, 'Active', GETDATE(), 1),
(N'STUDENT50', N'Ưu đãi sinh viên', N'Giảm 50,000 VNĐ cho sinh viên', 'Fixed', 50000, 300000, 50000, '2024-01-01', '2024-12-31', 500, 120, 'Active', GETDATE(), 1),
(N'SUMMER2024', N'Khuyến mãi hè', N'Giảm 15% tất cả khóa học trong mùa hè', 'Percentage', 15, 250000, 150000, '2024-06-01', '2024-08-31', 200, 0, 'Active', GETDATE(), 1),
(N'BLACKFRIDAY', N'Black Friday Sale', N'Giảm 30% trong ngày Black Friday', 'Percentage', 30, 500000, 200000, '2024-11-29', '2024-11-29', 50, 0, 'Inactive', GETDATE(), 1);

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Discounts.';
GO

-- =============================================
-- 14. THÊM DỮ LIỆU MẪU - FINANCES
-- =============================================
PRINT 'Đang thêm Finances...';

INSERT INTO Finances (Month, Year, Revenue, Fee, Type, Description, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy) VALUES
(1, 2024, 15000000, 1500000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 1/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(2, 2024, 18500000, 1850000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 2/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(3, 2024, 22000000, 2200000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 3/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(4, 2024, 25500000, 2550000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 4/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(5, 2024, 28000000, 2800000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 5/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(6, 2024, 32000000, 3200000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 6/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(7, 2024, 29500000, 2950000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 7/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(8, 2024, 31000000, 3100000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 8/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(9, 2024, 27500000, 2750000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 9/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(10, 2024, 35000000, 3500000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 10/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(11, 2024, 38000000, 3800000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 11/2024', GETDATE(), GETDATE(), 'admin', 'admin'),
(12, 2024, 42000000, 4200000, 'Course Sales', N'Doanh thu từ bán khóa học tháng 12/2024', GETDATE(), GETDATE(), 'admin', 'admin');

PRINT 'Đã thêm ' + CAST(@@ROWCOUNT AS VARCHAR) + ' Finances.';
GO

-- =============================================
-- 15. HOÀN THÀNH
-- =============================================
PRINT '==============================================';
PRINT 'HOÀN THÀNH VIỆC TẠO DỮ LIỆU MẪU!';
PRINT '==============================================';
PRINT 'Tổng kết:';
PRINT '- Categories: 8 records';
PRINT '- Courses: 12 records';
PRINT '- Chapters: 19 records';
PRINT '- Lessons: 9 records';
PRINT '- Questions: 4 records';
PRINT '- Answers: 4 records';
PRINT '- Enrollments: 8 records';
PRINT '- LessonProgresses: 8 records';
PRINT '- Certificates: 2 records';
PRINT '- Comments: 5 records';
PRINT '- Payments: 6 records';
PRINT '- Discounts: 4 records';
PRINT '- Finances: 12 records';
PRINT '==============================================';
PRINT 'Dữ liệu đã được tạo thành công với encoding UTF-8!';
PRINT 'Bạn có thể kiểm tra bằng cách truy cập website.';
PRINT '==============================================';
GO
