@model CourseDetailViewModel
@{
    ViewData["Title"] = Model.Course.Title;
}

<div class="container py-5">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Course Header -->
            <div class="mb-4" data-aos="fade-up">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Courses", "Home")">Khóa học</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Courses", "Home", new { categoryId = Model.Course.CategoryId })">@Model.Course.Category.Name</a></li>
                        <li class="breadcrumb-item active">@Model.Course.Title</li>
                    </ol>
                </nav>

                <h1 class="display-5 fw-bold mb-3">@Model.Course.Title</h1>

                <div class="d-flex flex-wrap align-items-center gap-3 mb-3">
                    <span class="badge bg-primary fs-6">@Model.Course.Category.Name</span>
                    <div class="rating">
                        @for (int i = 1; i <= 5; i++)
                        {
                            @if (i <= Model.Rating)
                            {
                                <i class="fas fa-star text-warning"></i>
                            }
                            else
                            {
                                <i class="far fa-star text-warning"></i>
                            }
                        }
                        <span class="ms-2">@Model.Rating.ToString("F1") (@Model.ReviewCount đánh giá)</span>
                    </div>
                    <span class="text-muted">
                        <i class="fas fa-users me-1"></i>@Model.Course.Enrollments.Count() học viên
                    </span>
                    <span class="text-muted">
                        <i class="fas fa-clock me-1"></i>Cập nhật @Model.Course.UpdatedAt?.ToString("dd/MM/yyyy") ?? @Model.Course.CreatedAt.ToString("dd/MM/yyyy")
                    </span>
                </div>
            </div>

            <!-- Course Video -->
            <div class="mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="ratio ratio-16x9">
                    @if (Model.Course.PreviewVideo.Contains("youtube.com") || Model.Course.PreviewVideo.Contains("youtu.be"))
                    {
                        string videoId;
                        if (Model.Course.PreviewVideo.Contains("youtu.be"))
                        {
                            videoId = Model.Course.PreviewVideo.Split('/').Last();
                        }
                        else
                        {
                            var parts = Model.Course.PreviewVideo.Split("v=");
                            if (parts.Length > 1)
                            {
                                videoId = parts[1].Split("&")[0];
                            }
                            else
                            {
                                videoId = "dQw4w9WgXcQ"; // Default video ID
                            }
                        }
                        <iframe src="https://www.youtube.com/embed/@videoId"
                                title="@Model.Course.Title"
                                allowfullscreen
                                class="rounded"></iframe>
                    }
                    else
                    {
                        <video controls class="w-100 rounded">
                            <source src="@Model.Course.PreviewVideo" type="video/mp4">
                            Trình duyệt của bạn không hỗ trợ video.
                        </video>
                    }
                </div>
            </div>

            <!-- Course Description -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card-body">
                    <h3 class="card-title">Mô tả khóa học</h3>
                    <div class="course-description">
                        @Html.Raw(Model.Course.Description)
                    </div>
                </div>
            </div>

            <!-- Course Content -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card-header">
                    <h3 class="mb-0">Nội dung khóa học</h3>
                </div>
                <div class="card-body">
                    @if (Model.Course.Chapters.Any())
                    {
                        <div class="accordion" id="courseContent">
                            @foreach (var chapter in Model.Course.Chapters.OrderBy(c => c.Id))
                            {
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="<EMAIL>">
                                        <button class="accordion-button @(Model.Course.Chapters.First() != chapter ? "collapsed" : "")"
                                                type="button" data-bs-toggle="collapse"
                                                data-bs-target="#<EMAIL>">
                                            <div class="d-flex justify-content-between w-100 me-3">
                                                <span><strong>@chapter.Name</strong></span>
                                                <span class="text-muted">Đang cập nhật nội dung</span>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="<EMAIL>"
                                         class="accordion-collapse collapse @(Model.Course.Chapters.First() == chapter ? "show" : "")"
                                         data-bs-parent="#courseContent">
                                        <div class="accordion-body">
                                            @if (chapter.Description != null)
                                            {
                                                <p class="text-muted mb-3">@chapter.Description</p>
                                            }

                                            <div class="text-center py-3">
                                                <i class="fas fa-book-open fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">Nội dung bài học đang được cập nhật</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Nội dung đang được cập nhật</h5>
                            <p class="text-muted">Khóa học này đang trong quá trình xây dựng nội dung.</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Reviews Section -->
            <div class="card" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header">
                    <h3 class="mb-0">Đánh giá từ học viên</h3>
                </div>
                <div class="card-body">
                    <div class="review-item mb-4 pb-3 border-bottom">
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-3">
                                <img src="https://via.placeholder.com/40x40/6366f1/ffffff?text=U"
                                     alt="User Demo"
                                     class="rounded-circle"
                                     style="width: 40px; height: 40px; object-fit: cover;">
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Học viên mẫu</h6>
                                <div class="rating">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                </div>
                            </div>
                            <small class="text-muted">@DateTime.Now.ToString("dd/MM/yyyy")</small>
                        </div>
                        <p class="mb-0">Khóa học rất hay và bổ ích. Nội dung được trình bày rõ ràng, dễ hiểu.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 100px;">
                <!-- Purchase Card -->
                <div class="card mb-4" data-aos="fade-left">
                    <div class="card-body text-center">
                        <img src="@Model.Course.Thumbnail" alt="@Model.Course.Title"
                             class="img-fluid rounded mb-3">

                        <div class="mb-3">
                            @if (Model.Course.Price == 0)
                            {
                                <h2 class="text-success fw-bold">Miễn phí</h2>
                            }
                            else
                            {
                                <h2 class="text-primary fw-bold">@Model.Course.Price.ToString("N0") VNĐ</h2>
                            }
                        </div>

                        @if (User.Identity!.IsAuthenticated)
                        {
                            @if (Model.IsEnrolled)
                            {
                                <a href="@Url.Action("MyCourses", "User")"
                                   class="btn btn-success btn-lg w-100 mb-3">
                                    <i class="fas fa-play me-2"></i>Tiếp tục học
                                </a>
                                <a href="@Url.Action("Dashboard", "User")"
                                   class="btn btn-outline-primary w-100">
                                    <i class="fas fa-tachometer-alt me-2"></i>Xem tiến độ
                                </a>
                            }
                            else
                            {
                                <div class="d-grid gap-2">
                                    @if (Model.Course.Price == 0)
                                    {
                                        <button type="button" class="btn btn-success btn-lg w-100"
                                                onclick="enrollCourse(@Model.Course.Id)">
                                            <i class="fas fa-download me-2"></i>Đăng ký miễn phí
                                        </button>
                                    }
                                    else
                                    {
                                        <a href="@Url.Action("Checkout", "Enrollment", new { id = Model.Course.Id })"
                                           class="btn btn-primary btn-lg w-100 mb-2">
                                            <i class="fas fa-shopping-cart me-2"></i>Đăng ký khóa học
                                        </a>
                                        <small class="text-muted text-center d-block">
                                            <i class="fas fa-shield-alt me-1"></i>Thanh toán an toàn & bảo mật
                                        </small>
                                    }
                                </div>
                            }
                        }
                        else
                        {
                            <div class="d-grid gap-2">
                                <a href="/Identity/Account/Login?returnUrl=@Url.Action("CourseDetail", "Home", new { id = Model.Course.Id })"
                                   class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để mua
                                </a>
                                <a href="/Identity/Account/Register" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>Đăng ký tài khoản
                                </a>
                            </div>
                        }

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>Đảm bảo hoàn tiền 30 ngày
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Course Info -->
                <div class="card mb-4" data-aos="fade-left" data-aos-delay="100">
                    <div class="card-header">
                        <h5 class="mb-0">Thông tin khóa học</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <i class="fas fa-book fa-2x text-primary mb-2"></i>
                                <div><strong>@Model.Course.Chapters.Count()</strong></div>
                                <small class="text-muted">Chương</small>
                            </div>
                            <div class="col-6 mb-3">
                                <i class="fas fa-play-circle fa-2x text-success mb-2"></i>
                                <div><strong>Đang cập nhật</strong></div>
                                <small class="text-muted">Bài học</small>
                            </div>
                            <div class="col-6 mb-3">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <div><strong>@(Model.Course.Duration?.ToString(@"hh\:mm") ?? "N/A")</strong></div>
                                <small class="text-muted">Thời lượng</small>
                            </div>
                            <div class="col-6 mb-3">
                                <i class="fas fa-certificate fa-2x text-info mb-2"></i>
                                <div><strong>Có</strong></div>
                                <small class="text-muted">Chứng chỉ</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Courses -->
                @if (Model.RelatedCourses.Any())
                {
                    <div class="card" data-aos="fade-left" data-aos-delay="200">
                        <div class="card-header">
                            <h5 class="mb-0">Khóa học liên quan</h5>
                        </div>
                        <div class="card-body">
                            @foreach (var relatedCourse in Model.RelatedCourses.Where(c => c.Id != Model.Course.Id).Take(3))
                            {
                                <div class="d-flex mb-3">
                                    <img src="@relatedCourse.Thumbnail" alt="@relatedCourse.Title"
                                         class="rounded me-3" style="width: 60px; height: 40px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="@Url.Action("CourseDetail", "Home", new { id = relatedCourse.Id })"
                                               class="text-decoration-none">@relatedCourse.Title</a>
                                        </h6>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">@relatedCourse.Enrollments.Count() học viên</small>
                                            @if (relatedCourse.Price == 0)
                                            {
                                                <span class="badge bg-success">Miễn phí</span>
                                            }
                                            else
                                            {
                                                <small class="text-primary fw-bold">@relatedCourse.Price.ToString("N0") VNĐ</small>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .course-description {
        line-height: 1.8;
    }

    .course-description p {
        margin-bottom: 1rem;
    }

    .review-item:last-child {
        border-bottom: none !important;
    }

    .sticky-top {
        z-index: 1020;
    }
</style>

<script>
    function enrollCourse(courseId) {
        if (!courseId) {
            showToast('Lỗi: Không tìm thấy thông tin khóa học!', 'error');
            return;
        }

        // Hiển thị loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xử lý...';
        button.disabled = true;

        // Gọi API đăng ký khóa học
        fetch('@Url.Action("ProcessEnrollment", "Enrollment")', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            },
            body: `courseId=${courseId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                if (data.redirectUrl) {
                    setTimeout(() => {
                        window.location.href = data.redirectUrl;
                    }, 1500);
                } else {
                    // Reload page để cập nhật trạng thái
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            } else {
                showToast(data.message, 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra, vui lòng thử lại!', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    function showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : type === 'info' ? 'info' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }

    // Thêm CSRF token vào trang
    document.addEventListener('DOMContentLoaded', function() {
        if (!document.querySelector('input[name="__RequestVerificationToken"]')) {
            const form = document.createElement('form');
            form.style.display = 'none';
            form.innerHTML = '@Html.AntiForgeryToken()';
            document.body.appendChild(form);
        }
    });
</script>
