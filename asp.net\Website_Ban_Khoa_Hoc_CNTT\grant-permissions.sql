-- Grant permissions to current user for ECourse database
USE ECourse;
GO

-- Add current user to database
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'DESKTOP-LJ825E1\Admin')
BEGIN
    CREATE USER [DESKTOP-LJ825E1\Admin] FROM LOGIN [DESKTOP-LJ825E1\Admin];
END
GO

-- Grant db_owner role to user
ALTER ROLE db_owner ADD MEMBER [DESKTOP-LJ825E1\Admin];
GO

-- Grant additional permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::dbo TO [DESKTOP-LJ825E1\Admin];
GO

PRINT 'Permissions granted successfully to DESKTOP-LJ825E1\Admin for ECourse database';
GO
