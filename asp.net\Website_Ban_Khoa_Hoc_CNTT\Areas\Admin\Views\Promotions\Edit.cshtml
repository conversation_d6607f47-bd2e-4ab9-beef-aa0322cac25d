@model PromotionEditViewModel
@{
    ViewData["Title"] = "Chỉnh sửa khuyến mãi";
}

<!-- Admin Edit Promotion -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-edit me-2"></i>
            Chỉnh sửa khuyến mãi: <span class="text-primary">@Model.Code</span>
        </h2>
        <div>
            <a asp-action="Details" asp-route-id="@Model.Id" class="admin-btn admin-btn-info me-2">
                <i class="fas fa-eye me-2"></i>
                Xem chi tiết
            </a>
            <a asp-action="Index" class="admin-btn admin-btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Quay lại danh sách
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin khuyến mãi</h5>
                </div>
                <div class="admin-card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden">
                        <input asp-for="CurrentUses" type="hidden">
                        <input asp-for="CreatedAt" type="hidden">
                        <input asp-for="UpdatedAt" type="hidden">
                        
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Code" class="form-label">Mã khuyến mãi <span class="text-danger">*</span></label>
                                <input asp-for="Code" class="form-control" style="text-transform: uppercase;">
                                <span asp-validation-for="Code" class="text-danger"></span>
                                <div class="form-text">Chỉ được chứa chữ hoa và số, không có khoảng trắng</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="DiscountPer" class="form-label">Phần trăm giảm giá (%) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input asp-for="DiscountPer" type="number" min="1" max="100" class="form-control">
                                    <span class="input-group-text">%</span>
                                </div>
                                <span asp-validation-for="DiscountPer" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="MaxUses" class="form-label">Số lần sử dụng tối đa <span class="text-danger">*</span></label>
                                <input asp-for="MaxUses" type="number" min="@Model.CurrentUses" class="form-control">
                                <span asp-validation-for="MaxUses" class="text-danger"></span>
                                <div class="form-text">Đã sử dụng: @Model.CurrentUses lần (không thể giảm xuống dưới số này)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="CourseId" class="form-label">Khóa học áp dụng <span class="text-danger">*</span></label>
                                <select asp-for="CourseId" class="form-select">
                                    <option value="">-- Chọn khóa học --</option>
                                    @foreach (var course in Model.AvailableCourses)
                                    {
                                        <option value="@course.Id" selected="@(course.Id == Model.CourseId)">
                                            @course.Title - @course.Price.ToString("N0") VNĐ
                                        </option>
                                    }
                                </select>
                                <span asp-validation-for="CourseId" class="text-danger"></span>
                                @if (Model.CurrentUses > 0)
                                {
                                    <div class="form-text text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Thay đổi khóa học có thể ảnh hưởng đến các đơn hàng đã sử dụng mã này
                                    </div>
                                }
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="StartDate" class="form-label">Ngày bắt đầu</label>
                                <input asp-for="StartDate" type="datetime-local" class="form-control">
                                <span asp-validation-for="StartDate" class="text-danger"></span>
                                <div class="form-text">Để trống nếu có hiệu lực ngay lập tức</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="EndDate" class="form-label">Ngày kết thúc</label>
                                <input asp-for="EndDate" type="datetime-local" class="form-control">
                                <span asp-validation-for="EndDate" class="text-danger"></span>
                                <div class="form-text">Để trống nếu không giới hạn thời gian</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox">
                                <label asp-for="IsActive" class="form-check-label">
                                    Kích hoạt khuyến mãi
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Hủy bỏ
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Cập nhật khuyến mãi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Status Card -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Trạng thái hiện tại</h5>
                </div>
                <div class="admin-card-body">
                    <div class="text-center mb-3">
                        <div class="badge bg-primary fs-6 mb-2">@Model.Code</div>
                        <div class="h4 text-success">@Model.DiscountPer% OFF</div>
                    </div>
                    
                    <div class="border rounded p-3 bg-light">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="text-muted small">Đã sử dụng</div>
                                <div class="fw-bold">@Model.CurrentUses/@Model.MaxUses</div>
                                <div class="progress mt-1" style="height: 6px;">
                                    @{
                                        var usagePercentage = Model.MaxUses > 0 ? (Model.CurrentUses * 100 / Model.MaxUses) : 0;
                                    }
                                    <div class="progress-bar" style="width: @usagePercentage%"></div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-muted small">Còn lại</div>
                                <div class="fw-bold">@(Model.MaxUses - Model.CurrentUses)</div>
                                <div class="small text-muted">lần sử dụng</div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="text-muted small">Thời gian:</div>
                        <div class="fw-bold small">
                            @if (Model.StartDate.HasValue && Model.EndDate.HasValue)
                            {
                                @Model.StartDate.Value.ToString("dd/MM/yyyy") <text> - </text> @Model.EndDate.Value.ToString("dd/MM/yyyy")
                            }
                            else if (Model.StartDate.HasValue)
                            {
                                <text>Từ </text>@Model.StartDate.Value.ToString("dd/MM/yyyy")
                            }
                            else if (Model.EndDate.HasValue)
                            {
                                <text>Đến </text>@Model.EndDate.Value.ToString("dd/MM/yyyy")
                            }
                            else
                            {
                                <text>Không giới hạn</text>
                            }
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="text-muted small">Trạng thái:</div>
                        @{
                            var status = "Active";
                            var statusClass = "bg-success";
                            
                            if (!Model.IsActive)
                            {
                                status = "Inactive";
                                statusClass = "bg-secondary";
                            }
                            else if (Model.EndDate.HasValue && Model.EndDate < DateTime.Now)
                            {
                                status = "Expired";
                                statusClass = "bg-danger";
                            }
                            else if (Model.StartDate.HasValue && Model.StartDate > DateTime.Now)
                            {
                                status = "Scheduled";
                                statusClass = "bg-warning";
                            }
                            else if (Model.CurrentUses >= Model.MaxUses)
                            {
                                status = "Used Up";
                                statusClass = "bg-dark";
                            }
                        }
                        <span class="badge @statusClass">@status</span>
                    </div>
                </div>
            </div>

            <!-- History Card -->
            <div class="admin-card mt-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">
                        <i class="fas fa-history me-2"></i>
                        Lịch sử
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <div class="fw-bold">Tạo khuyến mãi</div>
                                <div class="text-muted small">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                            </div>
                        </div>
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <div class="fw-bold">Cập nhật gần nhất</div>
                                    <div class="text-muted small">@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Auto uppercase code input
            $('#Code').on('input', function() {
                this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            });

            // Validate max uses
            $('#MaxUses').on('input', function() {
                var currentUses = @Model.CurrentUses;
                var maxUses = parseInt($(this).val());
                
                if (maxUses < currentUses) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').remove();
                    $(this).after('<div class="text-danger">Không thể giảm xuống dưới số lần đã sử dụng (' + currentUses + ')</div>');
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.text-danger').remove();
                }
            });

            // Form validation
            $('form').on('submit', function(e) {
                var startDate = $('#StartDate').val();
                var endDate = $('#EndDate').val();
                var maxUses = parseInt($('#MaxUses').val());
                var currentUses = @Model.CurrentUses;

                if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
                    e.preventDefault();
                    toastr.error('Ngày kết thúc phải sau ngày bắt đầu');
                    return false;
                }

                if (maxUses < currentUses) {
                    e.preventDefault();
                    toastr.error('Số lần sử dụng tối đa không thể nhỏ hơn số lần đã sử dụng');
                    return false;
                }
            });
        });
    </script>

    <style>
        .timeline {
            position: relative;
            padding-left: 20px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -16px;
            top: 4px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #fff;
        }
        
        .timeline-content {
            margin-left: 10px;
        }
    </style>
}
