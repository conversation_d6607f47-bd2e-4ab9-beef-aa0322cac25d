@model ELearningWebsite.Areas.Admin.ViewModels.LessonProgressDetailsViewModel
@{
    ViewData["Title"] = "Chi tiết Tiến độ Học tập";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-chart-line text-primary"></i>
                Chi tiết Tiến độ Học tập
            </h1>
            <p class="admin-page-subtitle">Thông tin chi tiết tiến độ học tập của "@Model.UserName"</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Progress Information -->
    <div class="col-lg-8">
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle text-primary"></i>
                    Thông tin Tiến độ
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-item">
                            <label>ID Tiến độ:</label>
                            <span class="detail-value">#@Model.Id</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-item">
                            <label>Trạng thái:</label>
                            <span class="badge bg-@(Model.IsCompleted ? "success" : Model.ProgressPercentage > 0 ? "warning" : "secondary") fs-6">
                                @Model.ProgressStatus
                            </span>
                            @if (Model.IsPassing)
                            {
                                <span class="badge bg-success ms-2">Đạt</span>
                            }
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="detail-item">
                            <label>Tiến độ hoàn thành:</label>
                            <div class="progress-detail">
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar bg-@(Model.IsCompleted ? "success" : Model.ProgressPercentage > 50 ? "warning" : "info")" 
                                         style="width: @Model.ProgressPercentage%">
                                        @Model.ProgressPercentage.ToString("F1")%
                                    </div>
                                </div>
                                <div class="progress-stats">
                                    <span class="text-primary">@Model.ProgressPercentage.ToString("F1")% hoàn thành</span>
                                    @if (Model.TimeSpent.HasValue)
                                    {
                                        <span class="text-muted ms-3">
                                            <i class="fas fa-clock"></i> @Model.TimeSpent.Value.ToString("F1") phút
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    @if (Model.HighestMark.HasValue)
                    {
                        <div class="col-md-6">
                            <div class="detail-item">
                                <label>Điểm cao nhất:</label>
                                <span class="detail-value">
                                    <span class="badge bg-@(Model.HighestMark >= 80 ? "success" : Model.HighestMark >= 60 ? "warning" : "danger") fs-6">
                                        @Model.HighestMark.Value.ToString("F1")
                                    </span>
                                </span>
                            </div>
                        </div>
                    }
                    @if (Model.CountDoing.HasValue)
                    {
                        <div class="col-md-6">
                            <div class="detail-item">
                                <label>Số lần thực hiện:</label>
                                <span class="detail-value text-info">@Model.CountDoing.Value lần</span>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Lesson Information -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-play-circle text-success"></i>
                    Thông tin Bài học
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-item">
                            <label>ID Bài học:</label>
                            <span class="detail-value">#@Model.LessonId</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-item">
                            <label>Loại bài học:</label>
                            <span class="badge bg-info">@Model.LessonType</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="detail-item">
                            <label>Tên bài học:</label>
                            <span class="detail-value">@Model.LessonTitle</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="detail-item">
                            <label>Khóa học:</label>
                            <span class="detail-value">@Model.CourseTitle</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="detail-item">
                            <label>Chương:</label>
                            <span class="detail-value">@Model.ChapterName</span>
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.LessonDescription))
                    {
                        <div class="col-12">
                            <div class="detail-item">
                                <label>Mô tả bài học:</label>
                                <div class="detail-value">
                                    @Html.Raw(Model.LessonDescription)
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- User Information -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-user text-info"></i>
                    Thông tin Học viên
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-item">
                            <label>ID Học viên:</label>
                            <span class="detail-value">@Model.UserId</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-item">
                            <label>Tên học viên:</label>
                            <span class="detail-value">@Model.UserName</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="detail-item">
                            <label>Email:</label>
                            <span class="detail-value">@Model.UserEmail</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress History -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-history text-warning"></i>
                    Lịch sử Tiến độ
                </h5>
            </div>
            <div class="card-body">
                @if (Model.ProgressHistory.Any())
                {
                    <div class="timeline">
                        @foreach (var history in Model.ProgressHistory.OrderByDescending(h => h.Date))
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-@(history.Action == "Completed" ? "success" : history.Action == "Started" ? "primary" : "info")"></div>
                                <div class="timeline-content">
                                    <h6>@history.Action</h6>
                                    <p class="text-muted mb-1">@history.Date.ToString("dd/MM/yyyy HH:mm")</p>
                                    <div class="timeline-details">
                                        <span class="badge bg-@(history.ProgressPercentage >= 100 ? "success" : history.ProgressPercentage > 0 ? "warning" : "secondary")">
                                            @history.ProgressPercentage.ToString("F1")%
                                        </span>
                                        @if (history.TimeSpent.HasValue)
                                        {
                                            <span class="text-muted ms-2">
                                                <i class="fas fa-clock"></i> @history.TimeSpent.Value.ToString("F1") phút
                                            </span>
                                        }
                                    </div>
                                    @if (!string.IsNullOrEmpty(history.Notes))
                                    {
                                        <small class="text-muted">@history.Notes</small>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Chưa có lịch sử</h6>
                        <p class="text-muted">Chưa có thay đổi nào được ghi nhận</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Stats -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-chart-pie text-primary"></i>
                    Thống kê nhanh
                </h5>
            </div>
            <div class="card-body">
                <div class="stat-item">
                    <div class="stat-number text-@(Model.IsCompleted ? "success" : Model.ProgressPercentage > 50 ? "warning" : "info")">
                        @Model.ProgressPercentage.ToString("F1")%
                    </div>
                    <div class="stat-label">Tiến độ hoàn thành</div>
                </div>
                @if (Model.TimeSpent.HasValue)
                {
                    <div class="stat-item">
                        <div class="stat-number text-info">@Model.TimeSpent.Value.ToString("F1")</div>
                        <div class="stat-label">Phút đã học</div>
                    </div>
                }
                @if (Model.CountDoing.HasValue)
                {
                    <div class="stat-item">
                        <div class="stat-number text-warning">@Model.CountDoing.Value</div>
                        <div class="stat-label">Lần thực hiện</div>
                    </div>
                }
                @if (Model.HighestMark.HasValue)
                {
                    <div class="stat-item">
                        <div class="stat-number text-@(Model.HighestMark >= 80 ? "success" : Model.HighestMark >= 60 ? "warning" : "danger")">
                            @Model.HighestMark.Value.ToString("F1")
                        </div>
                        <div class="stat-label">Điểm cao nhất</div>
                    </div>
                }
            </div>
        </div>

        <!-- Timeline -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-clock text-info"></i>
                    Thời gian
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>Bắt đầu học</h6>
                            <p class="text-muted mb-1">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                        </div>
                    </div>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <div class="timeline-item">
                            <div class="timeline-marker bg-@(Model.IsCompleted ? "success" : "warning")"></div>
                            <div class="timeline-content">
                                <h6>@(Model.IsCompleted ? "Hoàn thành" : "Cập nhật gần nhất")</h6>
                                <p class="text-muted mb-1">@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-tools text-secondary"></i>
                    Thao tác nhanh
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Chỉnh sửa tiến độ
                    </a>
                    <button type="button" class="btn btn-outline-success" onclick="updateProgress(100)">
                        <i class="fas fa-check"></i> Đánh dấu hoàn thành
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="updateProgress(0)">
                        <i class="fas fa-undo"></i> Reset tiến độ
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="showProgressModal()">
                        <i class="fas fa-percentage"></i> Cập nhật tiến độ
                    </button>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-outline-danger">
                        <i class="fas fa-trash"></i> Xóa tiến độ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div class="modal fade" id="updateProgressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cập nhật tiến độ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateProgressForm">
                    <div class="mb-3">
                        <label class="form-label">Tiến độ (%)</label>
                        <input type="number" id="newProgressValue" class="form-control" 
                               min="0" max="100" step="0.1" value="@Model.ProgressPercentage">
                        <div class="form-text">Nhập giá trị từ 0 đến 100</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveNewProgress()">Cập nhật</button>
            </div>
        </div>
    </div>
</div>

<style>
.stat-item {
    text-align: center;
    margin-bottom: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content p {
    font-size: 0.8rem;
}

.timeline-details {
    margin-top: 0.5rem;
}

.progress-detail {
    margin-top: 0.5rem;
}

.progress-stats {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}
</style>

<script>
function updateProgress(percentage) {
    if (confirm(`Bạn có chắc chắn muốn cập nhật tiến độ thành ${percentage}%?`)) {
        fetch(`/Admin/LessonProgresses/UpdateProgress/@Model.Id`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            },
            body: JSON.stringify({ progressPercentage: percentage })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                toastr.error(data.message);
            }
        })
        .catch(error => {
            toastr.error('Có lỗi xảy ra khi cập nhật tiến độ');
        });
    }
}

function showProgressModal() {
    new bootstrap.Modal(document.getElementById('updateProgressModal')).show();
}

function saveNewProgress() {
    const progress = document.getElementById('newProgressValue').value;
    
    fetch(`/Admin/LessonProgresses/UpdateProgress/@Model.Id`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
        },
        body: JSON.stringify({ progressPercentage: parseFloat(progress) })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            bootstrap.Modal.getInstance(document.getElementById('updateProgressModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            toastr.error(data.message);
        }
    })
    .catch(error => {
        toastr.error('Có lỗi xảy ra khi cập nhật tiến độ');
    });
}
</script>
