@model ELearningWebsite.Areas.Admin.ViewModels.LessonProgressEditViewModel
@{
    ViewData["Title"] = "Chỉnh sửa Tiến độ Học tập";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-edit text-primary"></i>
                Chỉnh sửa Tiến độ Học tập
            </h1>
            <p class="admin-page-subtitle">Cập nhật tiến độ học tập của "@Model.UserName"</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                <i class="fas fa-eye"></i> Xem chi tiết
            </a>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="admin-form-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-chart-line text-primary"></i>
                    Thông tin Tiến độ
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post" class="admin-form">
                    <input asp-for="Id" type="hidden">
                    
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="LessonId" class="form-label required">Bài học</label>
                                <select asp-for="LessonId" class="form-select" id="lessonSelect" onchange="updateLessonInfo()">
                                    <option value="">-- Chọn bài học --</option>
                                    @foreach (var lesson in Model.AvailableLessons)
                                    {
                                        <option value="@lesson.Id" 
                                                data-title="@lesson.Title"
                                                data-course="@lesson.CourseTitle"
                                                data-chapter="@lesson.ChapterName"
                                                data-type="@lesson.Type"
                                                selected="@(Model.LessonId == lesson.Id)">
                                            @lesson.Title (@lesson.CourseTitle - @lesson.ChapterName)
                                        </option>
                                    }
                                </select>
                                <span asp-validation-for="LessonId" class="text-danger"></span>
                                <small class="form-text text-muted">Thay đổi bài học có thể ảnh hưởng đến dữ liệu</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="UserId" class="form-label required">Học viên</label>
                                <select asp-for="UserId" class="form-select" id="userSelect" onchange="updateUserInfo()">
                                    <option value="">-- Chọn học viên --</option>
                                    @foreach (var user in Model.AvailableUsers)
                                    {
                                        <option value="@user.Id" 
                                                data-name="@user.Name"
                                                data-email="@user.Email"
                                                data-enrollments="@user.EnrollmentCount"
                                                data-completed="@user.CompletedLessons"
                                                selected="@(Model.UserId == user.Id)">
                                            @user.Name (@user.Email)
                                        </option>
                                    }
                                </select>
                                <span asp-validation-for="UserId" class="text-danger"></span>
                                <small class="form-text text-muted">Thay đổi học viên có thể ảnh hưởng đến dữ liệu</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ProgressPercentage" class="form-label required">Tiến độ (%)</label>
                                <input asp-for="ProgressPercentage" type="number" class="form-control" 
                                       min="0" max="100" step="0.1" onchange="updateProgressPreview()">
                                <span asp-validation-for="ProgressPercentage" class="text-danger"></span>
                                <small class="form-text text-muted">Nhập giá trị từ 0 đến 100</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TimeSpent" class="form-label">Thời gian học (phút)</label>
                                <input asp-for="TimeSpent" type="number" class="form-control" 
                                       min="0" step="0.1">
                                <span asp-validation-for="TimeSpent" class="text-danger"></span>
                                <small class="form-text text-muted">Thời gian đã dành để học bài này</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Status" class="form-label">Trạng thái</label>
                                <select asp-for="Status" class="form-select">
                                    <option value="Not Started">Chưa bắt đầu</option>
                                    <option value="In Progress">Đang học</option>
                                    <option value="Completed">Hoàn thành</option>
                                    <option value="Paused">Tạm dừng</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Passing" class="form-label">Kết quả</label>
                                <select asp-for="Passing" class="form-select">
                                    <option value="">Chưa xác định</option>
                                    <option value="1">Đạt</option>
                                    <option value="0">Không đạt</option>
                                </select>
                                <span asp-validation-for="Passing" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="CountDoing" class="form-label">Số lần thực hiện</label>
                                <input asp-for="CountDoing" type="number" class="form-control" min="1">
                                <span asp-validation-for="CountDoing" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="HighestMark" class="form-label">Điểm cao nhất</label>
                                <input asp-for="HighestMark" type="number" class="form-control" 
                                       min="0" max="100" step="0.1">
                                <span asp-validation-for="HighestMark" class="text-danger"></span>
                                <small class="form-text text-muted">Điểm cao nhất đạt được (nếu có)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Preview tiến độ</label>
                                <div class="progress" style="height: 20px;">
                                    <div id="progressPreview" class="progress-bar" style="width: @Model.ProgressPercentage%">
                                        @Model.ProgressPercentage.ToString("F1")%
                                    </div>
                                </div>
                                <small class="form-text text-muted">Xem trước thanh tiến độ</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Cập nhật
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Current Information -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle text-info"></i>
                    Thông tin hiện tại
                </h5>
            </div>
            <div class="card-body">
                <div class="detail-item">
                    <label>ID Tiến độ:</label>
                    <span class="detail-value">#@Model.Id</span>
                </div>
                <div class="detail-item">
                    <label>Bài học hiện tại:</label>
                    <span class="detail-value">@Model.LessonTitle</span>
                </div>
                <div class="detail-item">
                    <label>Học viên hiện tại:</label>
                    <span class="detail-value">@Model.UserName</span>
                </div>
                <div class="detail-item">
                    <label>Tiến độ hiện tại:</label>
                    <div class="progress mb-1" style="height: 8px;">
                        <div class="progress-bar bg-@(Model.ProgressPercentage >= 100 ? "success" : Model.ProgressPercentage > 50 ? "warning" : "info")" 
                             style="width: @Model.ProgressPercentage%"></div>
                    </div>
                    <small class="text-muted">@Model.ProgressPercentage.ToString("F1")%</small>
                </div>
                <div class="detail-item">
                    <label>Ngày tạo:</label>
                    <span class="detail-value">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                </div>
            </div>
        </div>

        <!-- Lesson Preview -->
        <div class="admin-detail-card" id="lessonPreview" style="display: none;">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-eye text-warning"></i>
                    Bài học mới (nếu thay đổi)
                </h5>
            </div>
            <div class="card-body">
                <div class="detail-item">
                    <label>Tên bài học:</label>
                    <span class="detail-value" id="previewLessonTitle">-</span>
                </div>
                <div class="detail-item">
                    <label>Khóa học:</label>
                    <span class="detail-value" id="previewCourse">-</span>
                </div>
                <div class="detail-item">
                    <label>Chương:</label>
                    <span class="detail-value" id="previewChapter">-</span>
                </div>
                <div class="detail-item">
                    <label>Loại:</label>
                    <span class="badge bg-info" id="previewType">-</span>
                </div>
            </div>
        </div>

        <!-- User Preview -->
        <div class="admin-detail-card" id="userPreview" style="display: none;">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-user text-warning"></i>
                    Học viên mới (nếu thay đổi)
                </h5>
            </div>
            <div class="card-body">
                <div class="detail-item">
                    <label>Tên học viên:</label>
                    <span class="detail-value" id="previewUserName">-</span>
                </div>
                <div class="detail-item">
                    <label>Email:</label>
                    <span class="detail-value" id="previewUserEmail">-</span>
                </div>
                <div class="detail-item">
                    <label>Số khóa học đã đăng ký:</label>
                    <span class="detail-value" id="previewEnrollments">-</span>
                </div>
                <div class="detail-item">
                    <label>Bài học đã hoàn thành:</label>
                    <span class="detail-value" id="previewCompleted">-</span>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-question-circle text-primary"></i>
                    Hướng dẫn
                </h5>
            </div>
            <div class="card-body">
                <div class="help-content">
                    <h6><i class="fas fa-edit text-primary"></i> Chỉnh sửa:</h6>
                    <ul class="help-list">
                        <li>Có thể thay đổi bài học và học viên</li>
                        <li>Cập nhật tiến độ và thời gian học</li>
                        <li>Thay đổi trạng thái và kết quả</li>
                        <li>Cập nhật điểm số nếu có</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle text-warning"></i> Lưu ý:</h6>
                    <ul class="help-list">
                        <li>Thay đổi bài học/học viên có thể tạo trùng lặp</li>
                        <li>Tiến độ 100% tự động đánh dấu hoàn thành</li>
                        <li>Thời gian học không thể âm</li>
                        <li>Số lần thực hiện tối thiểu là 1</li>
                    </ul>

                    <h6><i class="fas fa-tools text-secondary"></i> Thao tác:</h6>
                    <ul class="help-list">
                        <li>Kiểm tra thông tin trước khi lưu</li>
                        <li>Xem chi tiết sau khi cập nhật</li>
                        <li>Theo dõi lịch sử thay đổi</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateLessonInfo() {
    const select = document.getElementById('lessonSelect');
    const preview = document.getElementById('lessonPreview');
    
    if (select.value && select.value != '@Model.LessonId') {
        const option = select.options[select.selectedIndex];
        
        document.getElementById('previewLessonTitle').textContent = option.dataset.title;
        document.getElementById('previewCourse').textContent = option.dataset.course;
        document.getElementById('previewChapter').textContent = option.dataset.chapter;
        document.getElementById('previewType').textContent = option.dataset.type;
        
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

function updateUserInfo() {
    const select = document.getElementById('userSelect');
    const preview = document.getElementById('userPreview');
    
    if (select.value && select.value != '@Model.UserId') {
        const option = select.options[select.selectedIndex];
        
        document.getElementById('previewUserName').textContent = option.dataset.name;
        document.getElementById('previewUserEmail').textContent = option.dataset.email;
        document.getElementById('previewEnrollments').textContent = option.dataset.enrollments;
        document.getElementById('previewCompleted').textContent = option.dataset.completed;
        
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

function updateProgressPreview() {
    const progressInput = document.querySelector('input[name="ProgressPercentage"]');
    const progressBar = document.getElementById('progressPreview');
    
    const value = parseFloat(progressInput.value) || 0;
    const clampedValue = Math.max(0, Math.min(100, value));
    
    progressBar.style.width = clampedValue + '%';
    progressBar.textContent = clampedValue.toFixed(1) + '%';
    
    // Update color based on progress
    progressBar.className = 'progress-bar';
    if (clampedValue >= 100) {
        progressBar.classList.add('bg-success');
    } else if (clampedValue >= 50) {
        progressBar.classList.add('bg-warning');
    } else if (clampedValue > 0) {
        progressBar.classList.add('bg-info');
    } else {
        progressBar.classList.add('bg-secondary');
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateLessonInfo();
    updateUserInfo();
    updateProgressPreview();
});
</script>

<style>
.detail-item {
    margin-bottom: 1rem;
}

.detail-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    display: block;
}

.detail-value {
    font-weight: 500;
    color: #495057;
}

.help-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.help-content h6:first-child {
    margin-top: 0;
}

.help-list {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
    padding-left: 1.2rem;
}

.help-list li {
    margin-bottom: 0.25rem;
}
</style>
