# 🔧 Hướng dẫn Admin Area

## ✅ Đã hoàn thành

### 1. Tạo Admin Area hoàn chỉnh
- ✅ **Admin Controller** với authorization `[Authorize(Roles = "Admin")]`
- ✅ **Admin Layout** với sidebar navigation đẹp mắt
- ✅ **Dashboard** với thống kê tổng quan và biểu đồ
- ✅ **Routing** hỗ trợ Areas trong ASP.NET Core

### 2. Cấu trúc Admin Area
```
Areas/
└── Admin/
    ├── Controllers/
    │   └── HomeController.cs
    └── Views/
        ├── _ViewImports.cshtml
        ├── _ViewStart.cshtml
        ├── Home/
        │   └── Index.cshtml
        └── Shared/
            └── _AdminLayout.cshtml
```

### 3. Tính năng Dashboard
- 📊 **Thống kê tổng quan:**
  - Tổng số Users
  - Tổng số Courses  
  - Tổng số Enrollments
  - Tổng doanh thu

- 📈 **<PERSON><PERSON><PERSON><PERSON> đồ:**
  - Biểu đồ line chart thống kê theo tháng
  - <PERSON>iểu đồ doughnut chart tổng quan

- 📋 **Bảng dữ liệu:**
  - Users mới nhất
  - Courses mới nhất
  - Enrollments gần đây

## 🚀 Cách sử dụng

### Truy cập Admin Area
1. **Đăng nhập với tài khoản Admin:**
   - URL: `http://localhost:5000/Identity/Account/Login`
   - Email: `<EMAIL>`
   - Password: `Admin@123`

2. **Truy cập Dashboard:**
   - URL: `http://localhost:5000/Admin`
   - Hoặc click vào menu "Admin Panel" sau khi đăng nhập

### Phân quyền
- ✅ **Chỉ Admin mới truy cập được** - Có middleware `[Authorize(Roles = "Admin")]`
- ✅ **User khác sẽ bị từ chối** - Redirect về trang đăng nhập
- ✅ **Navigation an toàn** - Có link "Về trang chủ" để quay lại

## 🎨 Giao diện

### Sidebar Navigation
- 🎯 **Dashboard** - Trang chính (đã hoàn thành)
- 👥 **Quản lý Users** - Coming soon
- 📚 **Quản lý Courses** - Coming soon  
- 📂 **Categories** - Coming soon
- 🎓 **Enrollments** - Coming soon
- 💰 **Finances** - Coming soon
- 📊 **Reports** - Coming soon
- ⚙️ **Settings** - Coming soon

### Thiết kế
- 🎨 **Modern UI** với Bootstrap 5
- 🌈 **Gradient colors** cho sidebar và cards
- 📱 **Responsive design** 
- 🎯 **Hover effects** và animations
- 📊 **Chart.js** cho biểu đồ

## 🔧 Kỹ thuật

### Controller Features
```csharp
[Area("Admin")]
[Authorize(Roles = "Admin")]
public class HomeController : Controller
{
    // Dashboard với thống kê real-time
    // Queries tối ưu với Entity Framework
    // ViewModels cho data binding
}
```

### ViewModels
- `AdminDashboardViewModel` - Dữ liệu dashboard
- `MonthlyStatistic` - Thống kê theo tháng

### Database Queries
- Sử dụng **Entity Framework Core** với Include()
- **Async/await** cho performance
- **LINQ** queries tối ưu

## 📈 Thống kê hiển thị

### Cards thống kê:
1. **Total Users** - Tổng số người dùng
2. **Total Courses** - Tổng số khóa học
3. **Total Enrollments** - Tổng số đăng ký
4. **Total Revenue** - Tổng doanh thu (VND)

### Biểu đồ:
1. **Monthly Chart** - Enrollments và Revenue theo 6 tháng gần nhất
2. **Overview Chart** - Phân bố Users/Courses/Enrollments

### Bảng dữ liệu:
1. **Recent Users** - 5 user mới nhất
2. **Recent Courses** - 5 khóa học mới nhất

## 🛠️ Mở rộng

### Thêm trang quản lý mới:
1. Tạo Controller trong `Areas/Admin/Controllers/`
2. Thêm Views trong `Areas/Admin/Views/[ControllerName]/`
3. Cập nhật navigation trong `_AdminLayout.cshtml`

### Ví dụ thêm User Management:
```csharp
[Area("Admin")]
[Authorize(Roles = "Admin")]
public class UsersController : Controller
{
    public async Task<IActionResult> Index() { ... }
    public async Task<IActionResult> Edit(string id) { ... }
    public async Task<IActionResult> Delete(string id) { ... }
}
```

## 🔒 Bảo mật

### Authorization
- ✅ **Role-based** - Chỉ Admin mới truy cập
- ✅ **Area isolation** - Admin area tách biệt
- ✅ **Secure routing** - Không thể bypass qua URL

### Best Practices
- ✅ **Input validation** trong forms
- ✅ **CSRF protection** với AntiForgeryToken
- ✅ **SQL injection prevention** với Entity Framework

## 🐛 Troubleshooting

### Lỗi "Access Denied"
- Kiểm tra user có role "Admin" không
- Đảm bảo đã đăng nhập

### Lỗi "Page not found"
- Kiểm tra routing trong Program.cs
- Đảm bảo Areas được configure đúng

### Lỗi biểu đồ không hiển thị
- Kiểm tra Chart.js đã load chưa
- Kiểm tra data JSON trong view

## 📞 Hỗ trợ

### Kiểm tra logs:
```bash
# Xem logs trong terminal khi chạy dotnet run
# Kiểm tra Entity Framework queries
# Debug authorization issues
```

### Test accounts:
- **Admin:** <EMAIL> / Admin@123
- **Instructor:** <EMAIL> / Instructor@123  
- **Student:** <EMAIL> / Student@123

---
**Lưu ý:** Admin Area đã sẵn sàng sử dụng với dashboard đầy đủ tính năng! 🎉
