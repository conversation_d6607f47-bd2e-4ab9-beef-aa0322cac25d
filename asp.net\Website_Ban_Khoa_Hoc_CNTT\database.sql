/*
 Navicat Premium Data Transfer

 Source Server         : SQLSERVER
 Source Server Type    : SQL Server
 Source Server Version : 16004165
 Source Host           : ***************:1433
 Source Catalog        : ECourse
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 16004165
 File Encoding         : 65001

 Date: 28/03/2025 21:15:55
*/


-- ----------------------------
-- Table structure for __EFMigrationsHistory
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[__EFMigrationsHistory]') AND type IN ('U'))
	DROP TABLE [dbo].[__EFMigrationsHistory]
GO

CREATE TABLE [dbo].[__EFMigrationsHistory] (
  [MigrationId] nvarchar(150) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [ProductVersion] nvarchar(32) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL
)
GO

ALTER TABLE [dbo].[__EFMigrationsHistory] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of __EFMigrationsHistory
-- ----------------------------
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250302024508_AddDB', N'8.0.7')
GO

INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250310114731_realtion_lesson', N'8.0.7')
GO

INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250311023631_add_verifyEmail_User', N'8.0.7')
GO

INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250311072631_add_tokenEmail_user', N'8.0.7')
GO

INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250311145318_relationCourseChapter', N'8.0.7')
GO

INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250323032201_Add-Avatar', N'8.0.7')
GO

INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250326181449_add-highest-field', N'8.0.7')
GO


-- ----------------------------
-- Table structure for Answers
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Answers]') AND type IN ('U'))
	DROP TABLE [dbo].[Answers]
GO

CREATE TABLE [dbo].[Answers] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [QuestionId] int  NULL,
  [AnswerText] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [IsCorrect] bit  NOT NULL
)
GO

ALTER TABLE [dbo].[Answers] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Answers
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Answers] ON
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1022', N'1012', N'a', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1023', N'1012', N'b', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1024', N'1012', N'c', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1025', N'1013', N'a', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1026', N'1013', N'b', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1027', N'1013', N'c', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1028', N'1013', N'd', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1029', N'1014', N'e', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1030', N'1014', N'f', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1031', N'1014', N'g', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1032', N'1013', N'e', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1033', N'1014', N'cuong', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1034', N'1014', N'binh', N'0')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1035', N'1015', N'good', N'1')
GO

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1036', N'1015', N'bad', N'0')
GO

SET IDENTITY_INSERT [dbo].[Answers] OFF
GO


-- ----------------------------
-- Table structure for Categories
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Categories]') AND type IN ('U'))
	DROP TABLE [dbo].[Categories]
GO

CREATE TABLE [dbo].[Categories] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [Name] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [Description] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [Status] int  NOT NULL
)
GO

ALTER TABLE [dbo].[Categories] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Categories
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Categories] ON
GO

INSERT INTO [dbo].[Categories] ([Id], [Name], [Description], [Status]) VALUES (N'1', N'Kiến thức cơ sở', N'Lập trình cùng tvcdev', N'1')
GO

INSERT INTO [dbo].[Categories] ([Name], [Description], [Status]) VALUES 
(N'Lập trình Web', N'Các khóa học về phát triển web', 1),
(N'Lập trình Mobile', N'Các khóa học về phát triển ứng dụng di động', 1),
(N'Cơ sở dữ liệu', N'Các khóa học về quản trị và phát triển cơ sở dữ liệu', 1),
(N'DevOps & Cloud', N'Các khóa học về DevOps và điện toán đám mây', 1),
(N'AI & Machine Learning', N'Các khóa học về trí tuệ nhân tạo và học máy', 1),
(N'Blockchain & Web3', N'Các khóa học về blockchain và công nghệ Web3', 1),
(N'Bảo mật & Hacking', N'Các khóa học về an ninh mạng và bảo mật', 1);

SET IDENTITY_INSERT [dbo].[Categories] OFF
GO


-- ----------------------------
-- Table structure for Certificates
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Certificates]') AND type IN ('U'))
	DROP TABLE [dbo].[Certificates]
GO

CREATE TABLE [dbo].[Certificates] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [EnrollmentId] int  NOT NULL,
  [IssueDate] datetime2(7) DEFAULT getdate() NOT NULL,
  [CertificateNumber] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [CertificateUrl] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[Certificates] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Certificates
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Certificates] ON
GO

INSERT INTO [dbo].[Certificates] ([Id], [EnrollmentId], [IssueDate], [CertificateNumber], [CertificateUrl]) VALUES (N'14', N'4', N'2025-03-27 17:17:42.2013635', N'21202cda-3642-4d60-9d72-e7584048a9fe', N'https://elearningcourse.blob.core.windows.net/web/Certificate_4_638786926603997684.pdf')
GO

SET IDENTITY_INSERT [dbo].[Certificates] OFF
GO


-- ----------------------------
-- Table structure for Chapters
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Chapters]') AND type IN ('U'))
	DROP TABLE [dbo].[Chapters]
GO

CREATE TABLE [dbo].[Chapters] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [CourseId] int  NOT NULL,
  [Name] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [Description] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [Status] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [CreatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [UpdatedAt] datetime2(7)  NULL,
  [CreateBy] int  NOT NULL,
  [UpdateBy] int  NULL
)
GO

ALTER TABLE [dbo].[Chapters] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Chapters
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Chapters] ON
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1004', N'1012', N'Khái niệm ký thuật cần biết 1', NULL, N'Active', N'2025-02-26 16:54:38.0000000', N'2025-03-28 14:04:46.0698056', N'2', N'1')
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1005', N'1012', N'Khái niệm ký thuật cần biết 2', NULL, N'Active', N'2025-03-23 17:47:40.2966667', N'2025-03-28 14:04:46.1282229', N'1', N'1')
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1007', N'1012', N'Kĩ thuật cần biết 3', NULL, N'Active', N'2025-03-27 16:08:28.3900000', N'2025-03-28 14:04:46.1749791', N'1', N'1')
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1008', N'1025', N'Khái niệm ký thuật cần biết 2', NULL, N'Active', N'2025-03-28 04:14:06.2266667', NULL, N'1', NULL)
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1009', N'1012', N'Kĩ thuật cần biết 4', NULL, N'Active', N'2025-03-28 14:04:46.2200000', NULL, N'1', NULL)
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1027', N'1027', N'Giới thiệu ASP.NET Core MVC', N'Tổng quan về framework và setup môi trường', N'Active', GETDATE(), 2, 2)
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1027', N'1027', N'Working with Models', N'Làm việc với Models và Entity Framework Core', N'Active', GETDATE(), 2, 2)
GO

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1027', N'1027', N'Views & Controllers', N'Xây dựng Views và Controllers', N'Active', GETDATE(), 2, 2)
GO

SET IDENTITY_INSERT [dbo].[Chapters] OFF
GO


-- ----------------------------
-- Table structure for Comments
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Comments]') AND type IN ('U'))
	DROP TABLE [dbo].[Comments]
GO

CREATE TABLE [dbo].[Comments] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [LessonId] int  NOT NULL,
  [UserId] int  NULL,
  [ParentCommentId] int  NULL,
  [Content] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [CreatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [UpdatedAt] datetime2(7)  NULL,
  [IsDelete] bit DEFAULT CONVERT([bit],(0)) NOT NULL
)
GO

ALTER TABLE [dbo].[Comments] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Comments
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Comments] ON
GO

SET IDENTITY_INSERT [dbo].[Comments] OFF
GO


-- ----------------------------
-- Table structure for Courses
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Courses]') AND type IN ('U'))
	DROP TABLE [dbo].[Courses]
GO

CREATE TABLE [dbo].[Courses] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [Title] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [CategoryId] int  NOT NULL,
  [Price] float(53)  NOT NULL,
  [Thumbnail] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [Description] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [Duration] time(7)  NULL,
  [Status] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [PreviewVideo] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [CreatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [UpdatedAt] datetime2(7)  NULL,
  [LimitDay] int  NULL,
  [CreateBy] int  NOT NULL,
  [UpdateBy] int  NULL
)
GO

ALTER TABLE [dbo].[Courses] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Courses
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Courses] ON
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1012', N'Nhập môn kỹ thuật lập trình', N'1', N'0', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain là gì_ _ Bạn đã thật sự hiểu về domain_ _ Kiến thức nền tảng lập trình.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1017', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1018', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1019', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1020', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1021', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1024', N'Giải đề PE Prj301 | mới nhất', N'1', N'500000', N'https://elearningcourse.blob.core.windows.net/web/pe_prj_fall_2023%20%281%29.jpg', N'<p class="course__info-des">Để c&oacute; c&aacute;i nh&igrave;n tổng quan về ng&agrave;nh IT - Lập tr&igrave;nh web c&aacute;c bạn n&ecirc;n xem c&aacute;c videos tại kh&oacute;a n&agrave;y trước nh&eacute;</p>', NULL, N'pending', N'https://elearningcourse.blob.core.windows.net/web/b%E1%BA%A3o%20v%E1%BB%87%20m%C3%B4i.mp4', N'2025-03-23 13:57:54.6747791', N'2025-03-23 23:46:50.3018907', N'33', N'1023', N'1023')
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1025', N'login bang google', N'1', N'123123', N'https://elearningcourse.blob.core.windows.net/web/build_login_google.jpg', N'<p class="course__info-des">Để c&oacute; c&aacute;i nh&igrave;n tổng quan về ng&agrave;nh IT - Lập tr&igrave;nh web c&aacute;c bạn n&ecirc;n xem c&aacute;c videos tại kh&oacute;a n&agrave;y trước nh&eacute;.</p>
<div class="course__info-topic">
<h2 class="topic-heading">Bạn sẽ học được g&igrave;?</h2>
<div class="topic-content">
<ul class="topic-list">
<li>Giải Đề PE Java Web: N&acirc;ng Cao Kỹ Năng Servlet v&agrave; JSP</li>
<li>Thực H&agrave;nh Java Servlet v&agrave; JSP: Giải Quyết Đề Thi PE</li>
<li>Kh&oacute;a Học Giải Đề PE: Ph&aacute;t Triển Ứng Dụng Web với Java Servlet v&agrave; JSP</li>
<li>Học Thực H&agrave;nh Java Web: Giải Đề PE Servlet v&agrave; JSP</li>
</ul>
</div>
</div>', NULL, N'pending', N'https://elearningcourse.blob.core.windows.net/web/b%E1%BA%A3o%20v%E1%BB%87%20m%C3%B4i.mp4', N'2025-03-28 04:13:36.0460994', NULL, N'30', N'2', NULL)
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1026', N'MindMapGPT Course', N'1', N'0', N'https://elearningcourse.blob.core.windows.net/web/PostInFB_2.png', N'<p>Kho&aacute; học free về gpt</p>', NULL, N'pending', N'https://elearningcourse.blob.core.windows.net/web/2025-03-28%2020-59-12.mp4', N'2025-03-28 14:06:43.4763252', NULL, N'10', N'2', NULL)
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1027', N'ASP.NET Core MVC - Xây dựng ứng dụng web', 2, 1500000, 'https://elearningcourse.blob.core.windows.net/web/aspnet-mvc.jpg', 
N'<p>Khóa học ASP.NET Core MVC từ cơ bản đến nâng cao:</p><ul><li>Hiểu về mô hình MVC</li><li>Xây dựng ứng dụng web với C#</li><li>Entity Framework Core</li><li>Authentication & Authorization</li></ul>', 
'Published', 'https://elearningcourse.blob.core.windows.net/web/aspnet-preview.mp4', GETDATE(), 90, 2)
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1028', N'React.js - Frontend Development', 2, 1200000, 'https://elearningcourse.blob.core.windows.net/web/reactjs.jpg',
N'<p>Làm chủ React.js:</p><ul><li>Components và Props</li><li>State Management với Redux</li><li>React Hooks</li><li>Performance Optimization</li></ul>',
'Published', 'https://elearningcourse.blob.core.windows.net/web/react-preview.mp4', GETDATE(), 60, 2)
GO

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1029', N'SQL Server - Database Development', 4, 900000, 'https://elearningcourse.blob.core.windows.net/web/sql-server.jpg',
N'<p>Khóa học SQL Server toàn diện:</p><ul><li>T-SQL Programming</li><li>Database Design</li><li>Performance Tuning</li><li>Security</li></ul>',
'Published', 'https://elearningcourse.blob.core.windows.net/web/sql-preview.mp4', GETDATE(), 45, 2)
GO

SET IDENTITY_INSERT [dbo].[Courses] OFF
GO


-- ----------------------------
-- Table structure for Discounts
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Discounts]') AND type IN ('U'))
	DROP TABLE [dbo].[Discounts]
GO

CREATE TABLE [dbo].[Discounts] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [Code] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [DiscountPer] decimal(18,2)  NOT NULL,
  [MaxUses] int  NOT NULL,
  [StartDate] datetime2(7)  NULL,
  [EndDate] datetime2(7)  NULL,
  [CreatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [UpdatedAt] datetime2(7) DEFAULT getdate() NULL,
  [CreateBy] int  NOT NULL,
  [UpdateBy] int  NULL,
  [CourseId] int  NOT NULL
)
GO

ALTER TABLE [dbo].[Discounts] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Discounts
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Discounts] ON
GO

SET IDENTITY_INSERT [dbo].[Discounts] OFF
GO


-- ----------------------------
-- Table structure for Enrollments
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Enrollments]') AND type IN ('U'))
	DROP TABLE [dbo].[Enrollments]
GO

CREATE TABLE [dbo].[Enrollments] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [UserId] int  NOT NULL,
  [CourseId] int  NOT NULL,
  [EnrollmentDate] datetime2(7) DEFAULT getdate() NOT NULL,
  [Progress] float(53)  NOT NULL,
  [Status] int  NOT NULL,
  [ExpiredDate] datetime2(7)  NULL
)
GO

ALTER TABLE [dbo].[Enrollments] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Enrollments
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Enrollments] ON
GO

INSERT INTO [dbo].[Enrollments] ([Id], [UserId], [CourseId], [EnrollmentDate], [Progress], [Status], [ExpiredDate]) VALUES (N'4', N'1023', N'1012', N'2025-03-23 22:32:25.0000000', N'100', N'3', N'2025-05-15 22:32:35.0000000')
GO

INSERT INTO [dbo].[Enrollments] ([Id], [UserId], [CourseId], [EnrollmentDate], [Progress], [Status], [ExpiredDate]) VALUES (N'5', N'1023', N'1017', N'2025-03-18 16:00:53.0000000', N'0', N'2', N'2025-03-19 16:01:02.0000000')
GO

INSERT INTO [dbo].[Enrollments] ([Id], [UserId], [CourseId], [EnrollmentDate], [Progress], [Status], [ExpiredDate]) VALUES (N'13', N'1023', N'1019', N'2025-03-28 14:15:55.4477746', N'0', N'1', N'2025-04-27 14:15:55.4478091')
GO

SET IDENTITY_INSERT [dbo].[Enrollments] OFF
GO


-- ----------------------------
-- Table structure for Finances
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Finances]') AND type IN ('U'))
	DROP TABLE [dbo].[Finances]
GO

CREATE TABLE [dbo].[Finances] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [Month] int  NOT NULL,
  [Year] int  NOT NULL,
  [Revenue] float(53)  NOT NULL,
  [Fee] float(53)  NOT NULL,
  [Type] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [Description] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [CreatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [UpdatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [CreatedBy] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [UpdatedBy] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL
)
GO

ALTER TABLE [dbo].[Finances] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of Finances
-- ----------------------------
SET IDENTITY_INSERT [dbo].[Finances] ON
GO

SET IDENTITY_INSERT [dbo].[Finances] OFF
GO


-- ----------------------------
-- Table structure for LessonProgresses
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[LessonProgresses]') AND type IN ('U'))
	DROP TABLE [dbo].[LessonProgresses]
GO

CREATE TABLE [dbo].[LessonProgresses] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [LessonId] int  NOT NULL,
  [UserId] int  NOT NULL,
  [ProgressPercentage] real  NOT NULL,
  [TimeSpent] real  NULL,
  [CreatedAt] datetime2(7) DEFAULT getdate() NOT NULL,
  [UpdatedAt] datetime2(7)  NULL,
  [Status] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [Passing] int  NULL,
  [CountDoing] int  NULL,
  [HighestMark] real  NULL
)
GO

ALTER TABLE [dbo].[LessonProgresses] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of LessonProgresses
-- ----------------------------
SET IDENTITY_INSERT [dbo].[LessonProgresses] ON
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1002', N'1026', N'1023', N'30', N'0', N'2025-03-25 15:47:09.8559186', N'2025-03-25 15:47:09.8559963', NULL, N'1', N'7', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1003', N'1028', N'1023', N'0', N'0', N'2025-03-26 16:26:05.4701395', N'2025-03-26 16:26:05.4701830', NULL, N'1', N'11', N'10')
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1004', N'1032', N'1023', N'2.390519', N'0', N'2025-03-26 18:19:02.3835707', N'2025-03-26 18:19:02.3835756', NULL, N'1', N'5', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1005', N'1033', N'1023', N'0', N'0', N'2025-03-26 18:23:49.6967587', N'2025-03-26 18:23:49.6967642', NULL, N'1', N'1', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1006', N'1034', N'1023', N'6.555071', N'0', N'2025-03-26 18:24:11.8566093', N'2025-03-26 18:24:11.8566099', NULL, N'1', N'11', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1007', N'1035', N'1023', N'1.553416', N'0', N'2025-03-27 16:03:14.5105179', N'2025-03-27 16:03:14.5105491', NULL, N'1', N'12', N'61.74186')
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1008', N'1037', N'1023', N'5', N'0', N'2025-03-27 16:59:00.7399455', N'2025-03-27 16:59:00.7400079', NULL, N'1', N'5', N'10')
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1009', N'1036', N'1023', N'0', N'0', N'2025-03-27 17:00:29.9835200', N'2025-03-27 17:00:29.9835318', NULL, N'1', N'6', N'154.5755')
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1010', N'1038', N'1023', N'7.963766', N'0', N'2025-03-27 17:03:47.8176861', N'2025-03-27 17:03:47.8176992', NULL, N'1', N'20', N'7.963766')
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1011', N'1039', N'1023', N'0', N'0', N'2025-03-27 17:05:00.0000000', N'2025-03-27 17:05:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1012', N'1040', N'1023', N'0', N'0', N'2025-03-27 17:06:00.0000000', N'2025-03-27 17:06:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1013', N'1041', N'1023', N'0', N'0', N'2025-03-27 17:07:00.0000000', N'2025-03-27 17:07:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1014', N'1042', N'1023', N'0', N'0', N'2025-03-27 17:08:00.0000000', N'2025-03-27 17:08:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1015', N'1043', N'1023', N'0', N'0', N'2025-03-27 17:09:00.0000000', N'2025-03-27 17:09:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1016', N'1044', N'1023', N'0', N'0', N'2025-03-27 17:10:00.0000000', N'2025-03-27 17:10:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1017', N'1045', N'1023', N'0', N'0', N'2025-03-27 17:11:00.0000000', N'2025-03-27 17:11:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1018', N'1046', N'1023', N'0', N'0', N'2025-03-27 17:12:00.0000000', N'2025-03-27 17:12:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1019', N'1047', N'1023', N'0', N'0', N'2025-03-27 17:13:00.0000000', N'2025-03-27 17:13:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1020', N'1048', N'1023', N'0', N'0', N'2025-03-27 17:14:00.0000000', N'2025-03-27 17:14:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1021', N'1049', N'1023', N'0', N'0', N'2025-03-27 17:15:00.0000000', N'2025-03-27 17:15:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1022', N'1050', N'1023', N'0', N'0', N'2025-03-27 17:16:00.0000000', N'2025-03-27 17:16:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1023', N'1051', N'1023', N'0', N'0', N'2025-03-27 17:17:00.0000000', N'2025-03-27 17:17:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1024', N'1052', N'1023', N'0', N'0', N'2025-03-27 17:18:00.0000000', N'2025-03-27 17:18:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1025', N'1053', N'1023', N'0', N'0', N'2025-03-27 17:19:00.0000000', N'2025-03-27 17:19:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1026', N'1054', N'1023', N'0', N'0', N'2025-03-27 17:20:00.0000000', N'2025-03-27 17:20:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1027', N'1055', N'1023', N'0', N'0', N'2025-03-27 17:21:00.0000000', N'2025-03-27 17:21:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1028', N'1056', N'1023', N'0', N'0', N'2025-03-27 17:22:00.0000000', N'2025-03-27 17:22:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1029', N'1057', N'1023', N'0', N'0', N'2025-03-27 17:23:00.0000000', N'2025-03-27 17:23:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1030', N'1058', N'1023', N'0', N'0', N'2025-03-27 17:24:00.0000000', N'2025-03-27 17:24:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1031', N'1059', N'1023', N'0', N'0', N'2025-03-27 17:25:00.0000000', N'2025-03-27 17:25:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1032', N'1060', N'1023', N'0', N'0', N'2025-03-27 17:26:00.0000000', N'2025-03-27 17:26:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1033', N'1061', N'1023', N'0', N'0', N'2025-03-27 17:27:00.0000000', N'2025-03-27 17:27:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1034', N'1062', N'1023', N'0', N'0', N'2025-03-27 17:28:00.0000000', N'2025-03-27 17:28:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1035', N'1063', N'1023', N'0', N'0', N'2025-03-27 17:29:00.0000000', N'2025-03-27 17:29:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1036', N'1064', N'1023', N'0', N'0', N'2025-03-27 17:30:00.0000000', N'2025-03-27 17:30:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1037', N'1065', N'1023', N'0', N'0', N'2025-03-27 17:31:00.0000000', N'2025-03-27 17:31:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1038', N'1066', N'1023', N'0', N'0', N'2025-03-27 17:32:00.0000000', N'2025-03-27 17:32:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1039', N'1067', N'1023', N'0', N'0', N'2025-03-27 17:33:00.0000000', N'2025-03-27 17:33:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1040', N'1068', N'1023', N'0', N'0', N'2025-03-27 17:34:00.0000000', N'2025-03-27 17:34:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1041', N'1069', N'1023', N'0', N'0', N'2025-03-27 17:35:00.0000000', N'2025-03-27 17:35:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1042', N'1070', N'1023', N'0', N'0', N'2025-03-27 17:36:00.0000000', N'2025-03-27 17:36:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1043', N'1071', N'1023', N'0', N'0', N'2025-03-27 17:37:00.0000000', N'2025-03-27 17:37:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1044', N'1072', N'1023', N'0', N'0', N'2025-03-27 17:38:00.0000000', N'2025-03-27 17:38:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1045', N'1073', N'1023', N'0', N'0', N'2025-03-27 17:39:00.0000000', N'2025-03-27 17:39:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1046', N'1074', N'1023', N'0', N'0', N'2025-03-27 17:40:00.0000000', N'2025-03-27 17:40:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1047', N'1075', N'1023', N'0', N'0', N'2025-03-27 17:41:00.0000000', N'2025-03-27 17:41:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1048', N'1076', N'1023', N'0', N'0', N'2025-03-27 17:42:00.0000000', N'2025-03-27 17:42:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1049', N'1077', N'1023', N'0', N'0', N'2025-03-27 17:43:00.0000000', N'2025-03-27 17:43:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1050', N'1078', N'1023', N'0', N'0', N'2025-03-27 17:44:00.0000000', N'2025-03-27 17:44:00.0000000', NULL, N'1', N'3', NULL)
GO

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1026', N'1032', 100.0, 45.5, 'Completed', 1, 1, 95.5),
(N'1028', N'1032', 85.5, 35.0, 'In Progress', 1, 1, 85.5),
(N'1032', N'1032', 75.0, 60.0, 'In Progress', 1, 1, 75.0);

SET IDENTITY_INSERT [dbo].[LessonProgresses] OFF
GO
