/* Admin Dashboard Styles */

:root {
    --admin-primary: #1e293b;
    --admin-secondary: #334155;
    --admin-accent: #3b82f6;
    --admin-success: #10b981;
    --admin-warning: #f59e0b;
    --admin-danger: #ef4444;
    --admin-light: #f8fafc;
    --admin-border: #e2e8f0;
}

/* Sidebar */
.admin-sidebar {
    background: var(--admin-primary);
    min-height: 100vh;
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link {
    color: #94a3b8;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    margin: 0.25rem 1rem;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background: var(--admin-accent);
    color: white;
}

.admin-sidebar .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
}

/* Main Content */
.admin-main {
    margin-left: 250px;
    padding: 2rem;
    background: var(--admin-light);
    min-height: 100vh;
}

/* Header */
.admin-header {
    background: white;
    padding: 1rem 2rem;
    margin: -2rem -2rem 2rem -2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid var(--admin-border);
}

/* Cards */
.admin-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--admin-accent) 0%, #2563eb 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card.success {
    background: linear-gradient(135deg, var(--admin-success) 0%, #059669 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, var(--admin-warning) 0%, #d97706 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, var(--admin-danger) 0%, #dc2626 100%);
}

/* Tables */
.admin-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.admin-table .table {
    margin-bottom: 0;
}

.admin-table .table thead th {
    background: var(--admin-light);
    border: none;
    font-weight: 600;
    color: var(--admin-primary);
    padding: 1rem;
}

.admin-table .table tbody td {
    padding: 1rem;
    border-color: var(--admin-border);
    vertical-align: middle;
}

/* Buttons */
.btn-admin-primary {
    background: var(--admin-accent);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-admin-primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.btn-admin-success {
    background: var(--admin-success);
    border: none;
    border-radius: 8px;
    color: white;
}

.btn-admin-warning {
    background: var(--admin-warning);
    border: none;
    border-radius: 8px;
    color: white;
}

.btn-admin-danger {
    background: var(--admin-danger);
    border: none;
    border-radius: 8px;
    color: white;
}

/* Forms */
.admin-form .form-control {
    border: 2px solid var(--admin-border);
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.admin-form .form-control:focus {
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

.admin-form .form-label {
    font-weight: 600;
    color: var(--admin-primary);
    margin-bottom: 0.5rem;
}

/* Charts */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

/* Activity Feed */
.activity-item {
    padding: 1rem;
    border-left: 3px solid var(--admin-accent);
    margin-bottom: 1rem;
    background: white;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-item .activity-time {
    color: #64748b;
    font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
}

/* Utilities */
.text-admin-primary {
    color: var(--admin-primary) !important;
}

.text-admin-accent {
    color: var(--admin-accent) !important;
}

.bg-admin-light {
    background-color: var(--admin-light) !important;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--admin-border);
    border-top: 4px solid var(--admin-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
}

.status-badge.inactive {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--admin-warning);
}

/* Dropdown Menus */
.admin-dropdown {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
}

.admin-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.admin-dropdown .dropdown-item:hover {
    background: var(--admin-light);
    color: var(--admin-primary);
}
