@model ELearningWebsite.Areas.Admin.ViewModels.ChapterCreateViewModel
@{
    ViewData["Title"] = "Tạo Chương mới";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-plus-circle text-success"></i>
                Tạo Chương mới
            </h1>
            <p class="admin-page-subtitle">Thêm chương mới vào khóa học</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="admin-form-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-list-ol text-primary"></i>
                    Thông tin Chương
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" class="admin-form">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="CourseId" class="form-label required">Khóa học</label>
                                <select asp-for="CourseId" class="form-select" id="courseSelect" onchange="updateCourseInfo()">
                                    <option value="">-- Chọn khóa học --</option>
                                    @foreach (var course in Model.AvailableCourses)
                                    {
                                        <option value="@course.Id" 
                                                data-title="@course.Title"
                                                data-price="@course.Price"
                                                data-status="@course.Status"
                                                data-chapters="@course.ChapterCount">
                                            @course.Title (@course.Price.ToString("N0") VNĐ - @course.ChapterCount chương)
                                        </option>
                                    }
                                </select>
                                <span asp-validation-for="CourseId" class="text-danger"></span>
                                @if (!Model.AvailableCourses.Any())
                                {
                                    <div class="alert alert-warning mt-2">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Không có khóa học nào khả dụng. Vui lòng tạo khóa học trước.
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="Name" class="form-label required">Tên chương</label>
                                <input asp-for="Name" class="form-control" placeholder="Nhập tên chương">
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <small class="form-text text-muted">Tên chương nên ngắn gọn và mô tả rõ nội dung</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="Description" class="form-label">Mô tả</label>
                                <textarea asp-for="Description" class="form-control" rows="4" 
                                          placeholder="Mô tả chi tiết về nội dung chương học..."></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <small class="form-text text-muted">Mô tả chi tiết giúp học viên hiểu rõ nội dung chương</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Status" class="form-label required">Trạng thái</label>
                                <select asp-for="Status" class="form-select">
                                    <option value="Active">Hoạt động</option>
                                    <option value="Inactive">Không hoạt động</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                                <small class="form-text text-muted">Chương hoạt động sẽ hiển thị cho học viên</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Tạo chương
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Course Preview -->
        <div class="admin-detail-card" id="coursePreview" style="display: none;">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-eye text-info"></i>
                    Thông tin Khóa học
                </h5>
            </div>
            <div class="card-body">
                <div class="detail-item">
                    <label>Tên khóa học:</label>
                    <span class="detail-value" id="previewTitle">-</span>
                </div>
                <div class="detail-item">
                    <label>Giá:</label>
                    <span class="detail-value text-success" id="previewPrice">-</span>
                </div>
                <div class="detail-item">
                    <label>Trạng thái:</label>
                    <span class="badge" id="previewStatus">-</span>
                </div>
                <div class="detail-item">
                    <label>Số chương hiện tại:</label>
                    <span class="detail-value" id="previewChapters">-</span>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-question-circle text-primary"></i>
                    Hướng dẫn
                </h5>
            </div>
            <div class="card-body">
                <div class="help-content">
                    <h6><i class="fas fa-info-circle text-info"></i> Tạo chương:</h6>
                    <ul class="help-list">
                        <li>Chọn khóa học để thêm chương</li>
                        <li>Đặt tên chương ngắn gọn và rõ ràng</li>
                        <li>Viết mô tả chi tiết về nội dung</li>
                        <li>Chọn trạng thái phù hợp</li>
                    </ul>

                    <h6><i class="fas fa-lightbulb text-warning"></i> Lưu ý:</h6>
                    <ul class="help-list">
                        <li>Tên chương không được trùng lặp trong cùng khóa học</li>
                        <li>Chương "Hoạt động" sẽ hiển thị cho học viên</li>
                        <li>Có thể thay đổi trạng thái sau khi tạo</li>
                        <li>Mô tả hỗ trợ HTML cơ bản</li>
                    </ul>

                    <h6><i class="fas fa-tools text-secondary"></i> Sau khi tạo:</h6>
                    <ul class="help-list">
                        <li>Thêm bài học vào chương</li>
                        <li>Sắp xếp thứ tự bài học</li>
                        <li>Kiểm tra và publish chương</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Course Statistics -->
        @if (Model.AvailableCourses.Any())
        {
            <div class="admin-detail-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-chart-bar text-primary"></i>
                        Thống kê Khóa học
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stat-item">
                        <div class="stat-number text-info">@Model.AvailableCourses.Count</div>
                        <div class="stat-label">Tổng khóa học</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-success">@Model.AvailableCourses.Count(c => c.Status == "Published")</div>
                        <div class="stat-label">Đã xuất bản</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-warning">@Model.AvailableCourses.Sum(c => c.ChapterCount)</div>
                        <div class="stat-label">Tổng chương</div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<script>
function updateCourseInfo() {
    const select = document.getElementById('courseSelect');
    const preview = document.getElementById('coursePreview');
    
    if (select.value) {
        const option = select.options[select.selectedIndex];
        
        document.getElementById('previewTitle').textContent = option.dataset.title;
        document.getElementById('previewPrice').textContent = parseFloat(option.dataset.price).toLocaleString() + ' VNĐ';
        
        const statusBadge = document.getElementById('previewStatus');
        statusBadge.textContent = option.dataset.status;
        statusBadge.className = 'badge bg-' + (option.dataset.status === 'Published' ? 'success' : 'warning');
        
        document.getElementById('previewChapters').textContent = option.dataset.chapters + ' chương';
        
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}
</script>

<style>
.detail-item {
    margin-bottom: 1rem;
}

.detail-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    display: block;
}

.detail-value {
    font-weight: 500;
    color: #495057;
}

.help-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.help-content h6:first-child {
    margin-top: 0;
}

.help-list {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
    padding-left: 1.2rem;
}

.help-list li {
    margin-bottom: 0.25rem;
}

.stat-item {
    text-align: center;
    margin-bottom: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}
</style>
