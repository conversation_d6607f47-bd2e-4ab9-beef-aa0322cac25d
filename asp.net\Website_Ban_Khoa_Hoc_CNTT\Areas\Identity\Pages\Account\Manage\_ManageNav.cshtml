@using Microsoft.AspNetCore.Identity
@using ELearningWebsite.Models
@using ELearningWebsite.Areas.Identity.Pages.Account.Manage
@inject SignInManager<ApplicationUser> SignInManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-cog me-2"></i>
            Quản lý tài khoản
        </h5>
    </div>
    <div class="list-group list-group-flush">
        <a class="list-group-item list-group-item-action @ManageNavPages.IndexNavClass(ViewContext)" 
           asp-page="./Index">
            <i class="fas fa-user me-2"></i>
            Thông tin cá nhân
        </a>
        <a class="list-group-item list-group-item-action @ManageNavPages.EmailNavClass(ViewContext)" 
           asp-page="./Email">
            <i class="fas fa-envelope me-2"></i>
            Email
        </a>
        <a class="list-group-item list-group-item-action @ManageNavPages.ChangePasswordNavClass(ViewContext)" 
           asp-page="./ChangePassword">
            <i class="fas fa-key me-2"></i>
            Đổi mật khẩu
        </a>
        @if (hasExternalLogins)
        {
            <a class="list-group-item list-group-item-action @ManageNavPages.ExternalLoginsNavClass(ViewContext)" 
               asp-page="./ExternalLogins">
                <i class="fas fa-link me-2"></i>
                Liên kết tài khoản
            </a>
        }
        <a class="list-group-item list-group-item-action @ManageNavPages.TwoFactorAuthenticationNavClass(ViewContext)" 
           asp-page="./TwoFactorAuthentication">
            <i class="fas fa-shield-alt me-2"></i>
            Xác thực 2 bước
        </a>
        <a class="list-group-item list-group-item-action @ManageNavPages.PersonalDataNavClass(ViewContext)" 
           asp-page="./PersonalData">
            <i class="fas fa-download me-2"></i>
            Dữ liệu cá nhân
        </a>
    </div>
</div>
