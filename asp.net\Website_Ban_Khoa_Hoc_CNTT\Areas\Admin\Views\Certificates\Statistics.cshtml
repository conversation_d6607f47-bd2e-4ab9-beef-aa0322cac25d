@model ELearningWebsite.Areas.Admin.ViewModels.CertificateStatisticsViewModel
@{
    ViewData["Title"] = "Thống kê Chứng chỉ";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-chart-bar text-info"></i>
                Thống kê Chứng chỉ
            </h1>
            <p class="admin-page-subtitle">Báo cáo và phân tích dữ liệu chứng chỉ</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> Danh sách
            </a>
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus"></i> Tạo mới
            </a>
            <button onclick="window.print()" class="btn btn-outline-primary">
                <i class="fas fa-print"></i> In báo cáo
            </button>
        </div>
    </div>
</div>

<!-- Overview Cards -->
<div class="admin-summary-cards">
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-primary">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.TotalCertificates</h3>
                    <p>Tổng chứng chỉ</p>
                    <small class="text-muted">Tất cả chứng chỉ đã cấp</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-success">
                    <i class="fas fa-calendar-month"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.CertificatesThisMonth</h3>
                    <p>Tháng này</p>
                    <small class="text-muted">Chứng chỉ cấp trong tháng</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-info">
                    <i class="fas fa-calendar-year"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.CertificatesThisYear</h3>
                    <p>Năm này</p>
                    <small class="text-muted">Chứng chỉ cấp trong năm</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-summary-card">
                <div class="summary-icon bg-warning">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.CertificateCompletionRate.ToString("F1")%</h3>
                    <p>Tỷ lệ hoàn thành</p>
                    <small class="text-muted">Tỷ lệ có chứng chỉ/tổng đăng ký</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Certificate Trend Chart -->
    <div class="col-lg-8">
        <div class="admin-chart-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-chart-line text-primary"></i>
                    Xu hướng cấp chứng chỉ (30 ngày qua)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="certificateTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- File Status -->
    <div class="col-lg-4">
        <div class="admin-chart-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-file-pdf text-danger"></i>
                    Trạng thái File
                </h5>
            </div>
            <div class="card-body">
                <canvas id="fileStatusChart" height="200"></canvas>
                <div class="chart-legend mt-3">
                    <div class="legend-item">
                        <span class="legend-color bg-success"></span>
                        <span>Có file (@Model.CertificatesWithFiles)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color bg-warning"></span>
                        <span>Chưa có file (@Model.CertificatesWithoutFiles)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Courses -->
    <div class="col-lg-6">
        <div class="admin-table-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-trophy text-warning"></i>
                    Top khóa học có nhiều chứng chỉ nhất
                </h5>
            </div>
            <div class="card-body">
                @if (Model.TopCourses.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Khóa học</th>
                                    <th>Chứng chỉ</th>
                                    <th>Tỷ lệ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var course in Model.TopCourses)
                                {
                                    <tr>
                                        <td>
                                            <div class="course-info">
                                                <strong>@course.CourseTitle</strong>
                                                <br>
                                                <small class="text-muted">ID: @course.CourseId</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">@course.CertificateCount</span>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-success" style="width: @course.CompletionRate%"></div>
                                            </div>
                                            <small class="text-muted">@course.CompletionRate.ToString("F1")%</small>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Chưa có dữ liệu</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Recent Certificates -->
    <div class="col-lg-6">
        <div class="admin-table-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-clock text-info"></i>
                    Chứng chỉ mới nhất
                </h5>
            </div>
            <div class="card-body">
                @if (Model.RecentCertificates.Any())
                {
                    <div class="recent-certificates">
                        @foreach (var cert in Model.RecentCertificates)
                        {
                            <div class="recent-item">
                                <div class="recent-icon">
                                    <i class="fas fa-certificate text-warning"></i>
                                </div>
                                <div class="recent-content">
                                    <div class="recent-title">
                                        <a asp-action="Details" asp-route-id="@cert.Id" class="text-decoration-none">
                                            @cert.CertificateNumber
                                        </a>
                                    </div>
                                    <div class="recent-details">
                                        <small class="text-muted">
                                            @cert.UserName - @cert.CourseTitle
                                        </small>
                                    </div>
                                    <div class="recent-time">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i>
                                            @cert.IssueDate.ToString("dd/MM/yyyy HH:mm")
                                        </small>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-certificate fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Chưa có chứng chỉ nào</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row">
    <div class="col-lg-12">
        <div class="admin-detail-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle text-primary"></i>
                    Thông tin chi tiết
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-success">@Model.CompletedEnrollments</div>
                            <div class="stat-label">Đăng ký hoàn thành</div>
                            <small class="text-muted">Tổng số đăng ký đã hoàn thành khóa học</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-warning">@Model.PendingCertificates</div>
                            <div class="stat-label">Chờ cấp chứng chỉ</div>
                            <small class="text-muted">Đăng ký hoàn thành nhưng chưa có chứng chỉ</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-info">@Model.CertificatesWithFiles</div>
                            <div class="stat-label">Có file đính kèm</div>
                            <small class="text-muted">Chứng chỉ đã có file PDF</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="detail-stat">
                            <div class="stat-number text-secondary">@Model.CertificatesWithoutFiles</div>
                            <div class="stat-label">Chưa có file</div>
                            <small class="text-muted">Chứng chỉ chưa có file PDF</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Certificate Trend Chart
const trendCtx = document.getElementById('certificateTrendChart').getContext('2d');
const trendData = @Html.Raw(Json.Serialize(Model.TrendData.Select(t => new {
    date = t.Date.ToString("dd/MM"),
    count = t.Count
})));

new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: trendData.map(d => d.date),
        datasets: [{
            label: 'Chứng chỉ được cấp',
            data: trendData.map(d => d.count),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// File Status Chart
const fileCtx = document.getElementById('fileStatusChart').getContext('2d');
new Chart(fileCtx, {
    type: 'doughnut',
    data: {
        labels: ['Có file', 'Chưa có file'],
        datasets: [{
            data: [@Model.CertificatesWithFiles, @Model.CertificatesWithoutFiles],
            backgroundColor: ['#28a745', '#ffc107'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<style>
.recent-certificates {
    max-height: 400px;
    overflow-y: auto;
}

.recent-item {
    display: flex;
    align-items-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-icon {
    margin-right: 0.75rem;
    margin-top: 0.25rem;
}

.recent-content {
    flex: 1;
}

.recent-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.recent-details {
    margin-bottom: 0.25rem;
}

.recent-time {
    font-size: 0.8rem;
}

.detail-stat {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.course-info strong {
    font-size: 0.9rem;
}

/* Print styles */
.print-only {
    display: none;
}

.no-print {
    display: block;
}
</style>
