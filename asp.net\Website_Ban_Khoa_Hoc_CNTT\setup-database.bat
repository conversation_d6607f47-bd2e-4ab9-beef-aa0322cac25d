@echo off
echo Setting up database for ELearning CNTT Website...
echo.

echo Step 1: Creating initial migration...
dotnet ef migrations add InitialCreate

echo.
echo Step 2: Creating database and applying migrations...
dotnet ef database update

echo.
echo Step 3: Updating connection string to use ECourse database...
powershell -Command "(Get-Content appsettings.json) -replace 'Database=master', 'Database=ECourse' | Set-Content appsettings.json"

echo.
echo Database setup completed!
echo You can now run the application with: dotnet run
pause
