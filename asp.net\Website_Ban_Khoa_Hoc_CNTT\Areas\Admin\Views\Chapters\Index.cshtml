@model ELearningWebsite.Areas.Admin.ViewModels.ChapterIndexViewModel
@{
    ViewData["Title"] = "Quản lý Chương";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-list-ol text-primary"></i>
                Quản lý Chương
            </h1>
            <p class="admin-page-subtitle">Quản lý các chương trong khóa học</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus"></i> Tạo chương mới
            </a>
            <a asp-action="Statistics" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> Thống kê
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="admin-filters-card">
    <form method="get" class="admin-filters-form">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Tìm kiếm</label>
                <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" 
                       placeholder="Tên chương, mô tả, khóa học...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Khóa học</label>
                <select name="courseId" class="form-select">
                    <option value="">Tất cả khóa học</option>
                    @foreach (var course in Model.AvailableCourses)
                    {
                        <option value="@course.Id" selected="@(Model.CourseId == course.Id)">
                            @course.Title (@course.ChapterCount chương)
                        </option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="Active" selected="@(Model.Status == "Active")">Hoạt động</option>
                    <option value="Inactive" selected="@(Model.Status == "Inactive")">Không hoạt động</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Results Summary -->
<div class="admin-summary-cards">
    <div class="row">
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-primary">
                    <i class="fas fa-list-ol"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.TotalItems</h3>
                    <p>Tổng chương</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.Chapters.Count(c => c.IsActive)</h3>
                    <p>Đang hoạt động</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-warning">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.Chapters.Count(c => !c.IsActive)</h3>
                    <p>Không hoạt động</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-info">
                    <i class="fas fa-book"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.AvailableCourses.Count</h3>
                    <p>Khóa học</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chapters Table -->
<div class="admin-table-card">
    <div class="table-responsive">
        <table class="table admin-table">
            <thead>
                <tr>
                    <th>Tên chương</th>
                    <th>Khóa học</th>
                    <th>Trạng thái</th>
                    <th>Ngày tạo</th>
                    <th>Người tạo</th>
                    <th>Cập nhật</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.Chapters.Any())
                {
                    @foreach (var chapter in Model.Chapters)
                    {
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-list-ol text-primary me-2"></i>
                                    <div>
                                        <strong>@chapter.Name</strong>
                                        @if (!string.IsNullOrEmpty(chapter.Description))
                                        {
                                            <br>
                                            <small class="text-muted">@chapter.Description.Substring(0, Math.Min(50, chapter.Description.Length))@(chapter.Description.Length > 50 ? "..." : "")</small>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>@chapter.CourseName</strong>
                                    <br>
                                    <small class="text-muted">ID: @chapter.CourseId</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-@(chapter.IsActive ? "success" : "secondary")">
                                    @chapter.Status
                                </span>
                            </td>
                            <td>
                                <span class="text-primary">@chapter.CreatedAt.ToString("dd/MM/yyyy")</span>
                                <br>
                                <small class="text-muted">@chapter.CreatedAt.ToString("HH:mm")</small>
                            </td>
                            <td>
                                <span class="text-info">@chapter.CreatedByName</span>
                            </td>
                            <td>
                                @if (chapter.UpdatedAt.HasValue)
                                {
                                    <span class="text-success">@chapter.UpdatedAt.Value.ToString("dd/MM/yyyy")</span>
                                    <br>
                                    <small class="text-muted">@chapter.UpdatedByName</small>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa cập nhật</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@chapter.Id" 
                                       class="btn btn-sm btn-outline-info" title="Chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@chapter.Id" 
                                       class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-@(chapter.IsActive ? "warning" : "success")" 
                                            onclick="toggleStatus(@chapter.Id, '@chapter.Status')" 
                                            title="@(chapter.IsActive ? "Vô hiệu hóa" : "Kích hoạt")">
                                        <i class="fas fa-@(chapter.IsActive ? "pause" : "play")"></i>
                                    </button>
                                    <a asp-action="Delete" asp-route-id="@chapter.Id" 
                                       class="btn btn-sm btn-outline-danger" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="empty-state">
                                <i class="fas fa-list-ol fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Không có chương nào</h5>
                                <p class="text-muted">Chưa có chương nào được tạo hoặc không khớp với bộ lọc</p>
                                <a asp-action="Create" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Tạo chương đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
@if (Model.TotalPages > 1)
{
    <nav aria-label="Chapters pagination">
        <ul class="pagination justify-content-center">
            @if (Model.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" asp-action="Index" 
                       asp-route-page="@(Model.CurrentPage - 1)"
                       asp-route-searchTerm="@Model.SearchTerm"
                       asp-route-courseId="@Model.CourseId"
                       asp-route-status="@Model.Status">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                    <a class="page-link" asp-action="Index" 
                       asp-route-page="@i"
                       asp-route-searchTerm="@Model.SearchTerm"
                       asp-route-courseId="@Model.CourseId"
                       asp-route-status="@Model.Status">
                        @i
                    </a>
                </li>
            }

            @if (Model.CurrentPage < Model.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" asp-action="Index" 
                       asp-route-page="@(Model.CurrentPage + 1)"
                       asp-route-searchTerm="@Model.SearchTerm"
                       asp-route-courseId="@Model.CourseId"
                       asp-route-status="@Model.Status">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<script>
function toggleStatus(chapterId, currentStatus) {
    const action = currentStatus === 'Active' ? 'vô hiệu hóa' : 'kích hoạt';
    
    if (confirm(`Bạn có chắc chắn muốn ${action} chương này?`)) {
        fetch(`/Admin/Chapters/ToggleStatus/${chapterId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                toastr.error(data.message);
            }
        })
        .catch(error => {
            toastr.error('Có lỗi xảy ra khi thay đổi trạng thái');
        });
    }
}
</script>
