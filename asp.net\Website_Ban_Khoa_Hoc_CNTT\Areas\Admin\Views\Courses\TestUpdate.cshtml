@{
    ViewData["Title"] = "Test Update Thumbnail";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Test Update Thumbnail</h3>
                </div>
                <div class="card-body">
                    <div id="message"></div>

                    <form id="updateForm">
                        <div class="form-group mb-3">
                            <label for="courseId" class="form-label">Course ID</label>
                            <input type="number" id="courseId" name="courseId" class="form-control" value="1" required />
                        </div>

                        <div class="form-group mb-3">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" id="title" name="title" class="form-control" value="Test Course Updated" required />
                        </div>

                        <div class="form-group mb-3">
                            <label for="thumbnail" class="form-label">Thumbnail URL</label>
                            <input type="text" id="thumbnail" name="thumbnail" class="form-control" value="/images/courses/new-image.jpg" />
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Course
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('updateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('courseId', document.getElementById('courseId').value);
            formData.append('title', document.getElementById('title').value);
            formData.append('thumbnail', document.getElementById('thumbnail').value);
            
            // Add anti-forgery token
            const token = document.querySelector('input[name="__RequestVerificationToken"]');
            if (token) {
                formData.append('__RequestVerificationToken', token.value);
            }

            fetch('/Admin/Courses/UpdateCourseSimple', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('message');
                if (data.success) {
                    messageDiv.innerHTML = '<div class="alert alert-success">' + data.message + '</div>';
                } else {
                    messageDiv.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('message').innerHTML = '<div class="alert alert-danger">Có lỗi xảy ra khi gửi request</div>';
            });
        });
    });
</script>

@{
    // Add anti-forgery token
}
<input name="__RequestVerificationToken" type="hidden" value="@Html.AntiForgeryToken()" />
