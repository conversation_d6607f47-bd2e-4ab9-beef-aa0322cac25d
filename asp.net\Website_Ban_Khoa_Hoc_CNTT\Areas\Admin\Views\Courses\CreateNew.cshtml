@model Course

@{
    ViewData["Title"] = "Thêm khóa học mới";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        Thêm khóa học mới
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Title" class="form-label">Tên khóa học <span class="text-danger">*</span></label>
                                    <input asp-for="Title" class="form-control" placeholder="Nhập tên khóa học" />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CategoryId" class="form-label">Danh mục <span class="text-danger">*</span></label>
                                    <select asp-for="CategoryId" class="form-control">
                                        <option value="">-- Chọn danh mục --</option>
                                        @if (ViewBag.Categories != null)
                                        {
                                            @foreach (var category in (List<Category>)ViewBag.Categories)
                                            {
                                                <option value="@category.Id">@category.Name</option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="CategoryId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Price" class="form-label">Giá (VNĐ) <span class="text-danger">*</span></label>
                                    <input asp-for="Price" type="number" class="form-control" placeholder="0" min="0" />
                                    <span asp-validation-for="Price" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Duration" class="form-label">Thời lượng (phút)</label>
                                    <input asp-for="Duration" type="number" class="form-control" placeholder="0" min="0" />
                                    <span asp-validation-for="Duration" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="Description" class="form-label">Mô tả</label>
                            <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả khóa học"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Thumbnail" class="form-label">URL Thumbnail</label>
                                    <input asp-for="Thumbnail" class="form-control" placeholder="https://example.com/image.jpg" />
                                    <span asp-validation-for="Thumbnail" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PreviewVideo" class="form-label">URL Video Preview</label>
                                    <input asp-for="PreviewVideo" class="form-control" placeholder="https://example.com/video.mp4" />
                                    <span asp-validation-for="PreviewVideo" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="LimitDay" class="form-label">Thời hạn (ngày)</label>
                                    <input asp-for="LimitDay" type="number" class="form-control" placeholder="0" min="0" />
                                    <span asp-validation-for="LimitDay" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Status" class="form-label">Trạng thái</label>
                                    <select asp-for="Status" class="form-control">
                                        <option value="1">Hoạt động</option>
                                        <option value="0">Không hoạt động</option>
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu khóa học
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-eye"></i>
                        Xem trước
                    </h3>
                </div>
                <div class="card-body">
                    <div class="course-preview">
                        <img id="preview-thumbnail" src="https://via.placeholder.com/300x200?text=Thumbnail"
                             alt="Course Thumbnail" class="img-fluid rounded mb-3" style="width: 100%; height: 200px; object-fit: cover;">

                        <h5 id="preview-title" class="card-title">Tên khóa học</h5>
                        <p id="preview-description" class="card-text text-muted">Mô tả khóa học sẽ hiển thị ở đây...</p>

                        <div class="course-info">
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="fas fa-clock"></i> Thời lượng:</span>
                                <span id="preview-duration" class="font-weight-bold">0 phút</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="fas fa-tag"></i> Giá:</span>
                                <span id="preview-price" class="font-weight-bold text-primary">0 VNĐ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Live preview
        document.getElementById('Title').addEventListener('input', function() {
            document.getElementById('preview-title').textContent = this.value || 'Tên khóa học';
        });

        document.getElementById('Description').addEventListener('input', function() {
            document.getElementById('preview-description').textContent = this.value || 'Mô tả khóa học sẽ hiển thị ở đây...';
        });

        document.getElementById('Price').addEventListener('input', function() {
            const price = parseInt(this.value) || 0;
            document.getElementById('preview-price').textContent = price.toLocaleString('vi-VN') + ' VNĐ';
        });

        document.getElementById('Duration').addEventListener('input', function() {
            const duration = parseInt(this.value) || 0;
            document.getElementById('preview-duration').textContent = duration + ' phút';
        });

        document.getElementById('Thumbnail').addEventListener('input', function() {
            const url = this.value;
            if (url) {
                document.getElementById('preview-thumbnail').src = url;
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.getElementById('Title').value.trim();
            const categoryId = document.getElementById('CategoryId').value;
            const price = document.getElementById('Price').value;

            if (!title) {
                e.preventDefault();
                alert('Vui lòng nhập tên khóa học');
                document.getElementById('Title').focus();
                return;
            }

            if (!categoryId) {
                e.preventDefault();
                alert('Vui lòng chọn danh mục');
                document.getElementById('CategoryId').focus();
                return;
            }

            if (!price || price < 0) {
                e.preventDefault();
                alert('Vui lòng nhập giá hợp lệ');
                document.getElementById('Price').focus();
                return;
            }
        });
    });
</script>
