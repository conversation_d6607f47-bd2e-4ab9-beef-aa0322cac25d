@model MyCoursesViewModel
@{
    ViewData["Title"] = "Khóa học của tôi";
}

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1"><PERSON>h<PERSON>a học của tôi</h2>
                    <p class="text-muted mb-0">Quản lý và theo dõi tiến độ học tập</p>
                </div>
                <div>
                    <a href="@Url.Action("Dashboard", "User")" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="@Url.Action("Courses", "Home")" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><PERSON><PERSON><PERSON> kh<PERSON><PERSON> học mới
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link @(string.IsNullOrEmpty(Model.CurrentStatus) ? "active" : "")" 
                       href="@Url.Action("MyCourses", "User")">
                        <i class="fas fa-list me-2"></i>Tất cả (@Model.TotalEnrollments)
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(Model.CurrentStatus == "active" ? "active" : "")" 
                       href="@Url.Action("MyCourses", "User", new { status = "active" })">
                        <i class="fas fa-play-circle me-2"></i>Đang học
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(Model.CurrentStatus == "completed" ? "active" : "")" 
                       href="@Url.Action("MyCourses", "User", new { status = "completed" })">
                        <i class="fas fa-check-circle me-2"></i>Đã hoàn thành
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(Model.CurrentStatus == "suspended" ? "active" : "")" 
                       href="@Url.Action("MyCourses", "User", new { status = "suspended" })">
                        <i class="fas fa-pause-circle me-2"></i>Tạm dừng
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Courses Grid -->
    @if (Model.Enrollments.Any())
    {
        <div class="row">
            @foreach (var enrollment in Model.Enrollments)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 course-card">
                        <div class="position-relative">
                            <img src="@enrollment.Course.Thumbnail" alt="@enrollment.Course.Title" 
                                 class="card-img-top" style="height: 200px; object-fit: cover;">
                            
                            <!-- Status Badge -->
                            <span class="position-absolute top-0 end-0 m-2 badge bg-@(enrollment.Status == 1 ? "primary" : enrollment.Status == 3 ? "success" : "warning")">
                                @enrollment.GetStatusText()
                            </span>
                            
                            <!-- Progress Overlay -->
                            @if (enrollment.Progress > 0)
                            {
                                <div class="position-absolute bottom-0 start-0 w-100">
                                    <div class="progress" style="height: 4px; border-radius: 0;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: @enrollment.Progress%" 
                                             aria-valuenow="@enrollment.Progress" 
                                             aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            }
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-light text-dark">@enrollment.Course.Category.Name</span>
                            </div>
                            
                            <h5 class="card-title mb-2">
                                <a href="@Url.Action("CourseProgress", "User", new { id = enrollment.Id })" 
                                   class="text-decoration-none">@enrollment.Course.Title</a>
                            </h5>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">Tiến độ</small>
                                    <small class="fw-bold">@enrollment.Progress.ToString("F0")%</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: @enrollment.Progress%" 
                                         aria-valuenow="@enrollment.Progress" 
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3 flex-grow-1">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-muted d-block">Chương</small>
                                        <strong>@enrollment.Course.Chapters.Count()</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">Đăng ký</small>
                                        <strong>@enrollment.EnrollmentDate.ToString("dd/MM")</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">Hạn</small>
                                        <strong>@(enrollment.ExpiredDate?.ToString("dd/MM") ?? "Vô hạn")</strong>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-auto">
                                @if (enrollment.Status == 3) // Completed
                                {
                                    <div class="d-grid gap-2">
                                        <a href="@Url.Action("Certificate", "User", new { enrollmentId = enrollment.Id })" 
                                           class="btn btn-success">
                                            <i class="fas fa-certificate me-2"></i>Xem chứng chỉ
                                        </a>
                                        <a href="@Url.Action("CourseProgress", "User", new { id = enrollment.Id })" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-2"></i>Xem lại
                                        </a>
                                    </div>
                                }
                                else if (enrollment.IsExpired)
                                {
                                    <div class="d-grid">
                                        <button class="btn btn-outline-danger" disabled>
                                            <i class="fas fa-clock me-2"></i>Đã hết hạn
                                        </button>
                                    </div>
                                }
                                else
                                {
                                    <div class="d-grid gap-2">
                                        <a href="@Url.Action("CourseProgress", "User", new { id = enrollment.Id })" 
                                           class="btn btn-primary">
                                            <i class="fas fa-play me-2"></i>Tiếp tục học
                                        </a>
                                        @if (enrollment.Progress > 0)
                                        {
                                            <small class="text-center text-muted">
                                                Lần cuối học: @(enrollment.Course.UpdatedAt?.ToString("dd/MM/yyyy") ?? "N/A")
                                            </small>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Pagination -->
        @if (Model.TotalPages > 1)
        {
            <nav aria-label="Course pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    @if (Model.CurrentPage > 1)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("MyCourses", "User", new { status = Model.CurrentStatus, page = Model.CurrentPage - 1 })">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    }

                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                    {
                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                            <a class="page-link" href="@Url.Action("MyCourses", "User", new { status = Model.CurrentStatus, page = i })">@i</a>
                        </li>
                    }

                    @if (Model.CurrentPage < Model.TotalPages)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("MyCourses", "User", new { status = Model.CurrentStatus, page = Model.CurrentPage + 1 })">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    }
                </ul>
            </nav>
        }
    }
    else
    {
        <!-- Empty State -->
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-book-open fa-4x text-muted"></i>
            </div>
            <h4 class="text-muted mb-3">
                @if (string.IsNullOrEmpty(Model.CurrentStatus))
                {
                    <text>Chưa có khóa học nào</text>
                }
                else
                {
                    <text>Không có khóa học @(Model.CurrentStatus == "active" ? "đang học" : Model.CurrentStatus == "completed" ? "đã hoàn thành" : "tạm dừng")</text>
                }
            </h4>
            <p class="text-muted mb-4">Hãy khám phá và đăng ký các khóa học thú vị!</p>
            <a href="@Url.Action("Courses", "Home")" class="btn btn-primary btn-lg">
                <i class="fas fa-search me-2"></i>Tìm khóa học
            </a>
        </div>
    }
</div>

<style>
    .course-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .nav-pills .nav-link {
        color: #6c757d;
        border-radius: 0.375rem;
        margin-right: 0.5rem;
    }
    
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
        color: white;
    }
    
    .nav-pills .nav-link:hover:not(.active) {
        background-color: #f8f9fa;
        color: #0d6efd;
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .badge {
        font-size: 0.75em;
    }
</style>

<script>
    // Add loading animation
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.course-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
