# 📊 File INSERT Data Only

## 🎯 <PERSON><PERSON><PERSON> đích
File `insert-data-only.sql` được tạo từ việc tách tất cả các câu INSERT từ file `database.sql` gốc.

## 📁 Files được tạo
- **`insert-data-only.sql`** - Chỉ chứa các câu INSERT
- **`README-INSERT-DATA.md`** - File hướng dẫn này

## 📋 Nội dung file INSERT

### 🔧 Các bảng được import:

1. **__EFMigrationsHistory** - 7 records
   - Lịch sử migrations của Entity Framework

2. **Answers** - 15 records  
   - Đá<PERSON> án cho các câu hỏi trắc nghiệm

3. **Categories** - 8 records
   - <PERSON>h mục khóa học (Lập trình Web, Mobile, Database, etc.)

4. **Certificates** - 1 record
   - Chứng chỉ hoàn thành khóa học

5. **Chapters** - 8 records
   - <PERSON><PERSON><PERSON> chư<PERSON> trong kh<PERSON> học

6. **Courses** - 15 records
   - <PERSON><PERSON><PERSON><PERSON> h<PERSON> (bao gồm cả tiếng Việt và tiếng Anh)

7. **Enrollments** - 3 records
   - Đăng ký khóa học của học viên

8. **LessonProgresses** - 8 records (sample)
   - Tiến độ học tập (file gốc có rất nhiều records)

## 🚀 Cách sử dụng

### Điều kiện tiên quyết:
- Database `ECourseWeb` đã được tạo
- Schema (tables) đã được tạo từ migrations hoặc script tạo bảng
- SQL Server đang chạy

### Chạy file:

```bash
# Phương pháp 1: Command Line
sqlcmd -S "DESKTOP-LJ825E1\SQLSERVER" -E -i insert-data-only.sql

# Phương pháp 2: SSMS
# Mở file trong SQL Server Management Studio và thực thi
```

## ⚠️ Lưu ý quan trọng

### 🔴 Vấn đề encoding:
- Một số dữ liệu tiếng Việt có thể bị lỗi encoding
- Ví dụ: "Khóa học" có thể hiển thị thành "KhÃ³a há»c"
- **Khuyến nghị:** Sử dụng file `complete-sample-data.sql` thay thế

### 🔄 Thứ tự chạy:
- File này chỉ chứa INSERT, không có DELETE
- Nếu chạy nhiều lần sẽ bị lỗi duplicate key
- Cần xóa dữ liệu cũ trước khi chạy lại

### 📊 Dữ liệu không đầy đủ:
- LessonProgresses chỉ có sample data
- File gốc có hàng trăm records cho bảng này
- Một số bảng khác có thể thiếu dữ liệu

## 🔧 So sánh với các file khác

| File | Mục đích | Ưu điểm | Nhược điểm |
|------|----------|---------|------------|
| `database.sql` | File gốc đầy đủ | Có cả schema + data | Lớn, khó đọc |
| `insert-data-only.sql` | Chỉ INSERT data | Nhỏ gọn, dễ đọc | Lỗi encoding, thiếu data |
| `complete-sample-data.sql` | Data mẫu mới | Encoding đúng, đầy đủ | Không phải data gốc |

## 🎯 Khuyến nghị sử dụng

### ✅ Nên dùng khi:
- Muốn import data gốc từ hệ thống cũ
- Cần giữ nguyên ID và timestamps
- Đã có giải pháp cho vấn đề encoding

### ❌ Không nên dùng khi:
- Muốn data tiếng Việt hiển thị đúng
- Cần data hoàn chỉnh cho demo
- Chưa có kinh nghiệm xử lý encoding

## 🔄 Quy trình khuyến nghị

1. **Cho development/demo:**
   ```bash
   sqlcmd -S "DESKTOP-LJ825E1\SQLSERVER" -E -i complete-sample-data.sql
   ```

2. **Cho production với data gốc:**
   ```bash
   # Bước 1: Backup data hiện tại
   # Bước 2: Fix encoding issues
   # Bước 3: Chạy insert-data-only.sql
   sqlcmd -S "DESKTOP-LJ825E1\SQLSERVER" -E -i insert-data-only.sql
   ```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra database connection
2. Đảm bảo schema đã được tạo
3. Xem log lỗi để xác định vấn đề cụ thể
4. Cân nhắc sử dụng file `complete-sample-data.sql` thay thế

---
**Tạo từ:** database.sql (original)  
**Ngày tạo:** 2024-12-22  
**Tác giả:** ELearning Website Development Team
