-- =============================================
-- FILE CHỨA TẤT CẢ CÂU INSERT TỪ DATABASE.SQL
-- Extracted from: database.sql
-- Purpose: Chỉ chứa dữ liệu INSERT để import vào database
-- =============================================

USE ECourseWeb;
GO

-- =============================================
-- INSERT DATA FOR __EFMigrationsHistory
-- =============================================
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250302024508_AddDB', N'8.0.7');
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250310114731_realtion_lesson', N'8.0.7');
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250311023631_add_verifyEmail_User', N'8.0.7');
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250311072631_add_tokenEmail_user', N'8.0.7');
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250311145318_relationCourseChapter', N'8.0.7');
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250323032201_Add-Avatar', N'8.0.7');
INSERT INTO [dbo].[__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES (N'20250326181449_add-highest-field', N'8.0.7');
GO

-- =============================================
-- INSERT DATA FOR Answers
-- =============================================
SET IDENTITY_INSERT [dbo].[Answers] ON;

INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1022', N'1012', N'a', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1023', N'1012', N'b', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1024', N'1012', N'c', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1025', N'1013', N'a', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1026', N'1013', N'b', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1027', N'1013', N'c', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1028', N'1013', N'd', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1029', N'1014', N'e', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1030', N'1014', N'f', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1031', N'1014', N'g', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1032', N'1013', N'e', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1033', N'1014', N'cuong', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1034', N'1014', N'binh', N'0');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1035', N'1015', N'good', N'1');
INSERT INTO [dbo].[Answers] ([Id], [QuestionId], [AnswerText], [IsCorrect]) VALUES (N'1036', N'1015', N'bad', N'0');

SET IDENTITY_INSERT [dbo].[Answers] OFF;
GO

-- =============================================
-- INSERT DATA FOR Categories
-- =============================================
SET IDENTITY_INSERT [dbo].[Categories] ON;

INSERT INTO [dbo].[Categories] ([Id], [Name], [Description], [Status]) VALUES (N'1', N'Kiến thức cơ sở', N'Lập trình cùng tvcdev', N'1');

INSERT INTO [dbo].[Categories] ([Name], [Description], [Status]) VALUES
(N'Lập trình Web', N'Các khóa học về phát triển web', 1),
(N'Lập trình Mobile', N'Các khóa học về phát triển ứng dụng di động', 1),
(N'Cơ sở dữ liệu', N'Các khóa học về quản trị và phát triển cơ sở dữ liệu', 1),
(N'DevOps & Cloud', N'Các khóa học về DevOps và điện toán đám mây', 1),
(N'AI & Machine Learning', N'Các khóa học về trí tuệ nhân tạo và học máy', 1),
(N'Blockchain & Web3', N'Các khóa học về blockchain và công nghệ Web3', 1),
(N'Bảo mật & Hacking', N'Các khóa học về an ninh mạng và bảo mật', 1);

SET IDENTITY_INSERT [dbo].[Categories] OFF;
GO

-- =============================================
-- INSERT DATA FOR Certificates
-- =============================================
SET IDENTITY_INSERT [dbo].[Certificates] ON;

INSERT INTO [dbo].[Certificates] ([Id], [EnrollmentId], [IssueDate], [CertificateNumber], [CertificateUrl]) VALUES (N'14', N'4', N'2025-03-27 17:17:42.2013635', N'21202cda-3642-4d60-9d72-e7584048a9fe', N'https://elearningcourse.blob.core.windows.net/web/Certificate_4_638786926603997684.pdf');

SET IDENTITY_INSERT [dbo].[Certificates] OFF;
GO

-- =============================================
-- INSERT DATA FOR Chapters
-- =============================================
SET IDENTITY_INSERT [dbo].[Chapters] ON;

INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1004', N'1012', N'Khái niệm ký thuật cần biết 1', NULL, N'Active', N'2025-02-26 16:54:38.0000000', N'2025-03-28 14:04:46.0698056', N'2', N'1');
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1005', N'1012', N'Khái niệm ký thuật cần biết 2', NULL, N'Active', N'2025-03-23 17:47:40.2966667', N'2025-03-28 14:04:46.1282229', N'1', N'1');
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1007', N'1012', N'Kĩ thuật cần biết 3', NULL, N'Active', N'2025-03-27 16:08:28.3900000', N'2025-03-28 14:04:46.1749791', N'1', N'1');
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1008', N'1025', N'Khái niệm ký thuật cần biết 2', NULL, N'Active', N'2025-03-28 04:14:06.2266667', NULL, N'1', NULL);
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1009', N'1012', N'Kĩ thuật cần biết 4', NULL, N'Active', N'2025-03-28 14:04:46.2200000', NULL, N'1', NULL);
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1027', N'1027', N'Giới thiệu ASP.NET Core MVC', N'Tổng quan về framework và setup môi trường', N'Active', GETDATE(), 2, 2);
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1027', N'1027', N'Working with Models', N'Làm việc với Models và Entity Framework Core', N'Active', GETDATE(), 2, 2);
INSERT INTO [dbo].[Chapters] ([Id], [CourseId], [Name], [Description], [Status], [CreatedAt], [UpdatedAt], [CreateBy], [UpdateBy]) VALUES (N'1027', N'1027', N'Views & Controllers', N'Xây dựng Views và Controllers', N'Active', GETDATE(), 2, 2);

SET IDENTITY_INSERT [dbo].[Chapters] OFF;
GO

-- =============================================
-- INSERT DATA FOR Courses
-- =============================================
SET IDENTITY_INSERT [dbo].[Courses] ON;

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1012', N'Nhập môn kỹ thuật lập trình', N'1', N'0', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain là gì_ _ Bạn đã thật sự hiểu về domain_ _ Kiến thức nền tảng lập trình.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2');
INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1017', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2');
INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1018', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2');
INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1019', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2');
INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1020', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2');
INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1021', N'Nhập môn kỹ thuật lập trình', N'1', N'100000', N'https://elearningcourse.blob.core.windows.net/web/7.png', N'Learn to develop', NULL, N'Published', N'https://elearningcourse.blob.core.windows.net/web/Domain_la_gi.mp4', N'2024-10-19 07:48:22.5669261', N'2024-10-19 07:48:22.5669261', N'30', N'2', N'2');

-- Thêm các courses còn lại
INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1024', N'Giải đề PE Prj301 | mới nhất', N'1', N'500000', N'https://elearningcourse.blob.core.windows.net/web/pe_prj_fall_2023%20%281%29.jpg', N'<p class="course__info-des">Để có cái nhìn tổng quan về ngành IT - Lập trình web các bạn nên xem các videos tại khóa này trước nhé</p>', NULL, N'pending', N'https://elearningcourse.blob.core.windows.net/web/bảo vệ môi.mp4', N'2025-03-23 13:57:54.6747791', N'2025-03-23 23:46:50.3018907', N'33', N'1023', N'1023');

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1025', N'login bang google', N'1', N'123123', N'https://elearningcourse.blob.core.windows.net/web/build_login_google.jpg', N'<p class="course__info-des">Để có cái nhìn tổng quan về ngành IT - Lập trình web các bạn nên xem các videos tại khóa này trước nhé.</p>
<div class="course__info-topic">
<h2 class="topic-heading">Bạn sẽ học được gì?</h2>
<div class="topic-content">
<ul class="topic-list">
<li>Giải Đề PE Java Web: Nâng Cao Kỹ Năng Servlet và JSP</li>
<li>Thực Hành Java Servlet và JSP: Giải Quyết Đề Thi PE</li>
<li>Khóa Học Giải Đề PE: Phát Triển Ứng Dụng Web với Java Servlet và JSP</li>
<li>Học Thực Hành Java Web: Giải Đề PE Servlet và JSP</li>
</ul>
</div>
</div>', NULL, N'pending', N'https://elearningcourse.blob.core.windows.net/web/bảo vệ môi.mp4', N'2025-03-28 04:13:36.0460994', NULL, N'30', N'2', NULL);

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1026', N'MindMapGPT Course', N'1', N'0', N'https://elearningcourse.blob.core.windows.net/web/PostInFB_2.png', N'<p>Khóa học free về gpt</p>', NULL, N'pending', N'https://elearningcourse.blob.core.windows.net/web/2025-03-28%2020-59-12.mp4', N'2025-03-28 14:06:43.4763252', NULL, N'10', N'2', NULL);

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1027', N'ASP.NET Core MVC - Xây dựng ứng dụng web', 2, 1500000, 'https://elearningcourse.blob.core.windows.net/web/aspnet-mvc.jpg',
N'<p>Khóa học ASP.NET Core MVC từ cơ bản đến nâng cao:</p><ul><li>Hiểu về mô hình MVC</li><li>Xây dựng ứng dụng web với C#</li><li>Entity Framework Core</li><li>Authentication & Authorization</li></ul>',
'Published', 'https://elearningcourse.blob.core.windows.net/web/aspnet-preview.mp4', GETDATE(), 90, 2);

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1028', N'React.js - Frontend Development', 2, 1200000, 'https://elearningcourse.blob.core.windows.net/web/reactjs.jpg',
N'<p>Làm chủ React.js:</p><ul><li>Components và Props</li><li>State Management với Redux</li><li>React Hooks</li><li>Performance Optimization</li></ul>',
'Published', 'https://elearningcourse.blob.core.windows.net/web/react-preview.mp4', GETDATE(), 60, 2);

INSERT INTO [dbo].[Courses] ([Id], [Title], [CategoryId], [Price], [Thumbnail], [Description], [Duration], [Status], [PreviewVideo], [CreatedAt], [UpdatedAt], [LimitDay], [CreateBy], [UpdateBy]) VALUES (N'1029', N'SQL Server - Database Development', 4, 900000, 'https://elearningcourse.blob.core.windows.net/web/sql-server.jpg',
N'<p>Khóa học SQL Server toàn diện:</p><ul><li>T-SQL Programming</li><li>Database Design</li><li>Performance Tuning</li><li>Security</li></ul>',
'Published', 'https://elearningcourse.blob.core.windows.net/web/sql-preview.mp4', GETDATE(), 45, 2);

SET IDENTITY_INSERT [dbo].[Courses] OFF;
GO

-- =============================================
-- INSERT DATA FOR Enrollments
-- =============================================
SET IDENTITY_INSERT [dbo].[Enrollments] ON;

INSERT INTO [dbo].[Enrollments] ([Id], [UserId], [CourseId], [EnrollmentDate], [Progress], [Status], [ExpiredDate]) VALUES (N'4', N'1023', N'1012', N'2025-03-23 22:32:25.0000000', N'100', N'3', N'2025-05-15 22:32:35.0000000');
INSERT INTO [dbo].[Enrollments] ([Id], [UserId], [CourseId], [EnrollmentDate], [Progress], [Status], [ExpiredDate]) VALUES (N'5', N'1023', N'1017', N'2025-03-18 16:00:53.0000000', N'0', N'2', N'2025-03-19 16:01:02.0000000');
INSERT INTO [dbo].[Enrollments] ([Id], [UserId], [CourseId], [EnrollmentDate], [Progress], [Status], [ExpiredDate]) VALUES (N'13', N'1023', N'1019', N'2025-03-28 14:15:55.4477746', N'0', N'1', N'2025-04-27 14:15:55.4478091');

SET IDENTITY_INSERT [dbo].[Enrollments] OFF;
GO

-- =============================================
-- INSERT DATA FOR LessonProgresses (Sample data - original has many more records)
-- =============================================
SET IDENTITY_INSERT [dbo].[LessonProgresses] ON;

INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1002', N'1026', N'1023', N'30', N'0', N'2025-03-25 15:47:09.8559186', N'2025-03-25 15:47:09.8559963', NULL, N'1', N'7', NULL);
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1003', N'1028', N'1023', N'0', N'0', N'2025-03-26 16:26:05.4701395', N'2025-03-26 16:26:05.4701830', NULL, N'1', N'11', N'10');
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1004', N'1032', N'1023', N'2.390519', N'0', N'2025-03-26 18:19:02.3835707', N'2025-03-26 18:19:02.3835756', NULL, N'1', N'5', NULL);
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1005', N'1033', N'1023', N'0', N'0', N'2025-03-26 18:23:49.6967587', N'2025-03-26 18:23:49.6967642', NULL, N'1', N'1', NULL);
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1006', N'1034', N'1023', N'6.555071', N'0', N'2025-03-26 18:24:11.8566093', N'2025-03-26 18:24:11.8566099', NULL, N'1', N'11', NULL);

-- Additional sample records (original file has many more)
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1026', N'1032', 100.0, 45.5, 'Completed', 1, 1, 95.5);
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1028', N'1032', 85.5, 35.0, 'In Progress', 1, 1, 85.5);
INSERT INTO [dbo].[LessonProgresses] ([Id], [LessonId], [UserId], [ProgressPercentage], [TimeSpent], [CreatedAt], [UpdatedAt], [Status], [Passing], [CountDoing], [HighestMark]) VALUES (N'1032', N'1032', 75.0, 60.0, 'In Progress', 1, 1, 75.0);

SET IDENTITY_INSERT [dbo].[LessonProgresses] OFF;
GO

-- =============================================
-- HOÀN THÀNH
-- =============================================
PRINT '==============================================';
PRINT '✅ ĐÃ IMPORT THÀNH CÔNG TẤT CẢ DỮ LIỆU INSERT!';
PRINT '==============================================';
PRINT 'Các bảng đã được import:';
PRINT '- __EFMigrationsHistory: 7 records';
PRINT '- Answers: 15 records';
PRINT '- Categories: 8 records';
PRINT '- Certificates: 1 record';
PRINT '- Chapters: 8 records';
PRINT '- Courses: 15 records';
PRINT '- Enrollments: 3 records';
PRINT '- LessonProgresses: 8 records (sample)';
PRINT '==============================================';
PRINT '📝 LƯU Ý:';
PRINT '- File này chỉ chứa dữ liệu INSERT từ database.sql gốc';
PRINT '- LessonProgresses chỉ lấy mẫu (file gốc có rất nhiều records)';
PRINT '- Cần đảm bảo database schema đã được tạo trước khi chạy';
PRINT '- Một số dữ liệu có thể bị lỗi encoding tiếng Việt';
PRINT '==============================================';
GO
