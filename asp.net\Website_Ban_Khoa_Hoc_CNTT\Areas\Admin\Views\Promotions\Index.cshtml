@model PromotionIndexViewModel
@{
    ViewData["Title"] = "Quản lý Khuyến mãi";
}

<!-- Admin Promotions Management -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-tags me-2"></i>
            Quản lý Khuyến mãi
        </h2>
        <a asp-action="Create" class="admin-btn admin-btn-primary">
            <i class="fas fa-plus me-2"></i>
            Tạo khuyến mãi mới
        </a>
    </div>

    <!-- Filters -->
    <div class="admin-card mb-4">
        <div class="admin-card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Tìm kiếm</label>
                    <input type="text" name="search" value="@Model.SearchTerm" class="form-control" placeholder="Mã khuyến mãi hoặc tên khóa học...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Trạng thái</label>
                    <select name="status" class="form-select">
                        <option value="">Tất cả</option>
                        <option value="active" selected="@(Model.StatusFilter == "active")">Đang hoạt động</option>
                        <option value="inactive" selected="@(Model.StatusFilter == "inactive")">Không hoạt động</option>
                        <option value="expired" selected="@(Model.StatusFilter == "expired")">Hết hạn</option>
                        <option value="scheduled" selected="@(Model.StatusFilter == "scheduled")">Chờ kích hoạt</option>
                        <option value="usedup" selected="@(Model.StatusFilter == "usedup")">Hết lượt sử dụng</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Khóa học</label>
                    <select name="course" class="form-select">
                        <option value="">Tất cả khóa học</option>
                        @foreach (var course in Model.AvailableCourses)
                        {
                            <option value="@course.Id" selected="@(Model.CourseFilter == course.Id.ToString())">
                                @course.Title
                            </option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> Lọc
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Xóa bộ lọc
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-primary">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng khuyến mãi</div>
                            <div class="h5 mb-0 font-weight-bold">@Model.TotalItems</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-tags fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-success">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Đang hoạt động</div>
                            <div class="h5 mb-0 font-weight-bold" id="activeCount">-</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-warning">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Hết hạn</div>
                            <div class="h5 mb-0 font-weight-bold" id="expiredCount">-</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-info">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng lượt sử dụng</div>
                            <div class="h5 mb-0 font-weight-bold" id="totalUsage">-</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotions Table -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h5 class="admin-card-title">Danh sách khuyến mãi</h5>
        </div>
        <div class="admin-card-body">
            @if (Model.Promotions.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Mã khuyến mãi</th>
                                <th>Khóa học</th>
                                <th>Giảm giá</th>
                                <th>Sử dụng</th>
                                <th>Thời gian</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var promotion in Model.Promotions)
                            {
                                <tr>
                                    <td>
                                        <strong class="text-primary">@promotion.Code</strong>
                                        <br>
                                        <small class="text-muted">Tạo: @promotion.CreatedAt.ToString("dd/MM/yyyy")</small>
                                    </td>
                                    <td>
                                        <span class="fw-bold">@(promotion.CourseName.Length > 30 ? promotion.CourseName.Substring(0, 30) + "..." : promotion.CourseName)</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info fs-6">@promotion.DiscountPer%</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">@promotion.CurrentUses/@promotion.MaxUses</span>
                                            <div class="progress flex-grow-1" style="height: 8px;">
                                                <div class="progress-bar" role="progressbar" style="width: @promotion.UsagePercentage%"></div>
                                            </div>
                                        </div>
                                        <small class="text-muted">@promotion.UsagePercentage%</small>
                                    </td>
                                    <td>
                                        @if (promotion.StartDate.HasValue)
                                        {
                                            <small class="text-muted">Từ: @promotion.StartDate.Value.ToString("dd/MM/yyyy")</small><br>
                                        }
                                        @if (promotion.EndDate.HasValue)
                                        {
                                            <small class="text-muted">Đến: @promotion.EndDate.Value.ToString("dd/MM/yyyy")</small>
                                        }
                                        else
                                        {
                                            <small class="text-muted">Không giới hạn</small>
                                        }
                                    </td>
                                    <td>
                                        @{
                                            var statusClass = promotion.Status switch
                                            {
                                                "Active" => "bg-success",
                                                "Inactive" => "bg-secondary",
                                                "Expired" => "bg-danger",
                                                "Scheduled" => "bg-warning",
                                                "Used Up" => "bg-dark",
                                                _ => "bg-secondary"
                                            };
                                        }
                                        <span class="badge @statusClass">@promotion.Status</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@promotion.Id" class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@promotion.Id" class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-@(promotion.IsActive ? "warning" : "success")"
                                                    onclick="toggleStatus(@promotion.Id)" title="@(promotion.IsActive ? "Vô hiệu hóa" : "Kích hoạt")">
                                                <i class="fas fa-@(promotion.IsActive ? "pause" : "play")"></i>
                                            </button>
                                            @if (promotion.CurrentUses == 0)
                                            {
                                                <a asp-action="Delete" asp-route-id="@promotion.Id" class="btn btn-sm btn-outline-danger" title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="Pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            @if (Model.CurrentPage > 1)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1, search = Model.SearchTerm, status = Model.StatusFilter, course = Model.CourseFilter })">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("Index", new { page = i, search = Model.SearchTerm, status = Model.StatusFilter, course = Model.CourseFilter })">@i</a>
                                </li>
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1, search = Model.SearchTerm, status = Model.StatusFilter, course = Model.CourseFilter })">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có khuyến mãi nào</h5>
                    <p class="text-muted">Hãy tạo khuyến mãi đầu tiên để thu hút khách hàng!</p>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Tạo khuyến mãi mới
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Load statistics on page load
        $(document).ready(function() {
            loadStatistics();
        });

        function loadStatistics() {
            $.get('@Url.Action("Statistics")', function(data) {
                $('#activeCount').text(data.activePromotions);
                $('#expiredCount').text(data.expiredPromotions);
                $('#totalUsage').text(data.totalUsage);
            });
        }

        function toggleStatus(id) {
            if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái khuyến mãi này?')) {
                $.post('@Url.Action("ToggleStatus")/' + id, function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error(response.message);
                    }
                }).fail(function() {
                    toastr.error('Có lỗi xảy ra khi thay đổi trạng thái');
                });
            }
        }

        // Auto-submit form when filter changes
        $('select[name="status"], select[name="course"]').change(function() {
            $(this).closest('form').submit();
        });
    </script>
}
