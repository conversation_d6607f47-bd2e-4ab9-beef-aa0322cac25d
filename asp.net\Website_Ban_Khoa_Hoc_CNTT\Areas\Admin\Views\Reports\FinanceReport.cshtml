@model FinanceReportViewModel
@{
    ViewData["Title"] = "Báo cáo Tài chính";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-chart-line me-2"></i>
            Báo cáo Tài chính
        </h2>
        <div>
            <button class="admin-btn admin-btn-primary" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>
                Xuất Excel
            </button>
        </div>
    </div>

    <!-- Finance Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 mb-3">
            <div class="admin-card stat-card-success">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">T<PERSON><PERSON> doanh thu</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalRevenue.ToString("N0") VNĐ</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 mb-3">
            <div class="admin-card stat-card-warning">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng phí</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalFees.ToString("N0") VNĐ</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-hand-holding-usd fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 mb-3">
            <div class="admin-card stat-card-primary">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Doanh thu ròng</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.NetRevenue.ToString("N0") VNĐ</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-coins fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 mb-3">
            <div class="admin-card stat-card-info">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng giao dịch</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalTransactions</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-exchange-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Trend Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Xu hướng Doanh thu (12 tháng gần đây)</h5>
                </div>
                <div class="admin-card-body">
                    <canvas id="revenueTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Table -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Chi tiết Doanh thu theo Tháng</h5>
                </div>
                <div class="admin-card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tháng</th>
                                    <th>Doanh thu</th>
                                    <th>Phí</th>
                                    <th>Doanh thu ròng</th>
                                    <th>Tỷ lệ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var month in Model.RevenueTrend)
                                {
                                    var profitMargin = month.Revenue > 0 
                                        ? (month.NetRevenue * 100.0 / month.Revenue) 
                                        : 0;
                                    <tr>
                                        <td>@month.Month</td>
                                        <td>@month.Revenue.ToString("N0") VNĐ</td>
                                        <td>@month.Fees.ToString("N0") VNĐ</td>
                                        <td>@month.NetRevenue.ToString("N0") VNĐ</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: @profitMargin%"
                                                     title="Tỷ suất lợi nhuận: @profitMargin.ToString("F1")%">
                                                    @profitMargin.ToString("F1")%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Trend Chart
        var ctx = document.getElementById('revenueTrendChart').getContext('2d');
        var data = {
            labels: [@Html.Raw(string.Join(",", Model.RevenueTrend.Select(x => $"'{x.Month}'")))],
            datasets: [{
                label: 'Doanh thu',
                data: [@string.Join(",", Model.RevenueTrend.Select(x => x.Revenue))],
                backgroundColor: 'rgba(40, 167, 69, 0.2)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1,
                yAxisID: 'y'
            }, {
                label: 'Doanh thu ròng',
                data: [@string.Join(",", Model.RevenueTrend.Select(x => x.NetRevenue))],
                backgroundColor: 'rgba(0, 123, 255, 0.2)',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 1,
                yAxisID: 'y'
            }]
        };
        var options = {
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'VNĐ'
                    }
                }
            }
        };
        new Chart(ctx, {
            type: 'line',
            data: data,
            options: options
        });

        // Export function
        function exportToExcel() {
            fetch('/Admin/Reports/ExportToExcel?reportType=finance', {
                method: 'POST'
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Có lỗi xảy ra khi xuất Excel');
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'BaoCaoTaiChinh.xlsx';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            })
            .catch(error => {
                alert(error.message);
            });
        }
    </script>
} 