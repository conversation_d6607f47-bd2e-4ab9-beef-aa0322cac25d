@model Finance
@{
    ViewData["Title"] = "Chỉnh sửa Tài chính";
}

<!-- Admin Edit Finance -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-edit me-2"></i>
            Chỉnh sửa Tài chính #@Model.Id
        </h2>
        <div>
            <a asp-action="Details" asp-route-id="@Model.Id" class="admin-btn admin-btn-info me-2">
                <i class="fas fa-eye me-2"></i>
                Xem chi tiết
            </a>
            <a asp-action="Index" class="admin-btn admin-btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin tài chính</h5>
                </div>
                <div class="admin-card-body">
                    <form asp-action="Edit" method="post">
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedAt" />
                        <input type="hidden" asp-for="CreatedBy" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Month" class="form-label">
                                        Tháng <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Month" class="form-control" required>
                                        @for (int i = 1; i <= 12; i++)
                                        {
                                            <option value="@i">Tháng @i</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Month" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Year" class="form-label">
                                        Năm <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Year" class="form-control" required>
                                        @for (int i = DateTime.Now.Year; i >= DateTime.Now.Year - 5; i--)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Year" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Revenue" class="form-label">
                                        Doanh thu <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input asp-for="Revenue" type="number" step="0.01" class="form-control" 
                                               placeholder="Nhập doanh thu" required />
                                        <span class="input-group-text">VNĐ</span>
                                    </div>
                                    <span asp-validation-for="Revenue" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Fee" class="form-label">Phí</label>
                                    <div class="input-group">
                                        <input asp-for="Fee" type="number" step="0.01" class="form-control" 
                                               placeholder="Nhập phí (tùy chọn)" />
                                        <span class="input-group-text">VNĐ</span>
                                    </div>
                                    <span asp-validation-for="Fee" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label asp-for="Type" class="form-label">Loại</label>
                                    <select asp-for="Type" class="form-control">
                                        <option value="">-- Chọn loại --</option>
                                        <option value="Course Sale">Bán khóa học</option>
                                        <option value="Subscription">Đăng ký</option>
                                        <option value="Commission">Hoa hồng</option>
                                        <option value="Refund">Hoàn tiền</option>
                                        <option value="Other">Khác</option>
                                    </select>
                                    <span asp-validation-for="Type" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label asp-for="Description" class="form-label">Mô tả</label>
                                    <textarea asp-for="Description" class="form-control" rows="4" 
                                              placeholder="Nhập mô tả chi tiết (tùy chọn)"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Cập nhật bản ghi
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info ms-2">
                                <i class="fas fa-eye me-2"></i>
                                Xem chi tiết
                            </a>
                            <a asp-action="Index" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>
                                Hủy bỏ
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thông tin hiện tại</h5>
                </div>
                <div class="admin-card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>ID:</strong></td>
                            <td>@Model.Id</td>
                        </tr>
                        <tr>
                            <td><strong>Tháng/Năm hiện tại:</strong></td>
                            <td>
                                <span class="badge bg-info">@Model.Month/@Model.Year</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Doanh thu hiện tại:</strong></td>
                            <td>
                                <span class="fw-bold text-success">@Model.Revenue.ToString("N0") VNĐ</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Phí hiện tại:</strong></td>
                            <td>
                                <span class="fw-bold text-warning">@Model.Fee.ToString("N0") VNĐ</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Doanh thu ròng:</strong></td>
                            <td>
                                @{
                                    var netRevenue = Model.Revenue - Model.Fee;
                                    var netClass = netRevenue >= 0 ? "text-primary" : "text-danger";
                                }
                                <span class="fw-bold @netClass">@netRevenue.ToString("N0") VNĐ</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Loại hiện tại:</strong></td>
                            <td>
                                @if (!string.IsNullOrEmpty(Model.Type))
                                {
                                    <span class="badge bg-secondary">@Model.Type</span>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa phân loại</span>
                                }
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Lịch sử thay đổi</h5>
                </div>
                <div class="admin-card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Ngày tạo:</strong></td>
                            <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                        </tr>
                        <tr>
                            <td><strong>Người tạo:</strong></td>
                            <td>@Model.CreatedBy</td>
                        </tr>
                        <tr>
                            <td><strong>Lần cập nhật cuối:</strong></td>
                            <td>@Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                        </tr>
                        <tr>
                            <td><strong>Người cập nhật:</strong></td>
                            <td>@Model.UpdatedBy</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Hướng dẫn</h5>
                </div>
                <div class="admin-card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Lưu ý khi chỉnh sửa:</h6>
                        <ul class="mb-0">
                            <li>Thay đổi sẽ được ghi lại thời gian</li>
                            <li>Doanh thu phải là số dương</li>
                            <li>Kiểm tra kỹ trước khi lưu</li>
                            <li>Có thể xem chi tiết sau khi lưu</li>
                        </ul>
                    </div>

                    @if (netRevenue < 0)
                    {
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Cảnh báo:</h6>
                            <p class="mb-0">
                                Doanh thu ròng hiện tại âm! Hãy kiểm tra lại số liệu.
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Calculate net revenue in real-time
            function calculateNet() {
                const revenue = parseFloat($('#Revenue').val()) || 0;
                const fee = parseFloat($('#Fee').val()) || 0;
                const net = revenue - fee;
                
                if (!$('#net-display').length) {
                    $('#Fee').closest('.form-group').after(
                        '<div id="net-display" class="alert alert-light mt-2">' +
                        '<strong>Doanh thu ròng mới: <span id="net-amount">0</span> VNĐ</strong>' +
                        '</div>'
                    );
                }
                
                $('#net-amount').text(net.toLocaleString());
                
                if (net < 0) {
                    $('#net-display').removeClass('alert-light alert-success').addClass('alert-warning');
                } else {
                    $('#net-display').removeClass('alert-light alert-warning').addClass('alert-success');
                }
            }
            
            $('#Revenue, #Fee').on('input', calculateNet);
            calculateNet(); // Initial calculation
        });
    </script>
}
