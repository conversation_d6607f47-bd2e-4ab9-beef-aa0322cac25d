# HƯỚNG DẪN SỬ DỤNG HỆ THỐNG QUẢN LÝ KHÓA HỌC CNTT

## 🚀 KHỞI ĐỘNG HỆ THỐNG

### Yêu cầu hệ thống:
- .NET 6.0 hoặc cao hơn
- SQL Server hoặc SQL Server Express
- Visual Studio 2022 hoặc VS Code

### Cách chạy ứng dụng:
```bash
cd asp.net\Website_Ban_Khoa_Hoc_CNTT
dotnet run --urls "http://localhost:5000"
```

Truy cập: **http://localhost:5000**

---

## 👤 TÀI KHOẢN MẶC ĐỊNH

### Tài khoản Admin:
- **Email:** <EMAIL>
- **Password:** Admin123!
- **Quyền:** Toàn quyền quản trị hệ thống

### Tài khoản User thử nghiệm:
- **Email:** <EMAIL>  
- **Password:** User123!
- **Quyền:** Người dùng thường

### Tạo tài khoản mới:
1. <PERSON><PERSON><PERSON> cậ<PERSON> trang đăng ký: `/Account/Register`
2. <PERSON><PERSON><PERSON><PERSON> thông tin đầy đủ
3. X<PERSON><PERSON> nhận email (nếu được cấu hình)

---

## 🏠 GIAO DIỆN NGƯỜI DÙNG

### Trang chủ:
- Hiển thị danh sách khóa học nổi bật
- Tìm kiếm khóa học theo từ khóa
- Lọc theo danh mục, mức độ, giá

### Trang khóa học:
- Xem chi tiết khóa học
- Đăng ký học
- Xem nội dung bài học (sau khi đăng ký)
- Theo dõi tiến độ học tập

### Trang cá nhân:
- Quản lý thông tin cá nhân
- Xem khóa học đã đăng ký
- Theo dõi tiến độ học tập
- Xem chứng chỉ đã đạt được

---

## ⚙️ HỆ THỐNG QUẢN TRỊ ADMIN

### Đăng nhập Admin:
1. Truy cập: `/Admin` hoặc `/Account/Login`
2. Đăng nhập bằng tài khoản admin
3. Tự động chuyển hướng đến Dashboard

### Dashboard Admin:
- Thống kê tổng quan hệ thống
- Biểu đồ người dùng, khóa học, doanh thu
- Hoạt động gần đây

---

## 📚 QUẢN LÝ KHÓA HỌC

### Danh sách khóa học:
- **Đường dẫn:** `/Admin/Courses`
- **Chức năng:**
  - Xem danh sách tất cả khóa học
  - Tìm kiếm theo tên, mô tả
  - Lọc theo danh mục, trạng thái, giá
  - Sắp xếp theo ngày tạo, cập nhật

### Tạo khóa học mới:
- **Đường dẫn:** `/Admin/Courses/Create`
- **Thông tin cần nhập:**
  - Tên khóa học
  - Mô tả ngắn và chi tiết
  - Danh mục
  - Mức độ (Beginner, Intermediate, Advanced)
  - Giá và giá khuyến mãi
  - Hình ảnh đại diện
  - Video giới thiệu

### Chỉnh sửa khóa học:
- **Đường dẫn:** `/Admin/Courses/Edit/{id}`
- **Chức năng:**
  - Cập nhật thông tin khóa học
  - Thay đổi trạng thái (Draft, Published, Archived)
  - Quản lý nội dung bài học

### Xóa khóa học:
- **Đường dẫn:** `/Admin/Courses/Delete/{id}`
- **Lưu ý:** Kiểm tra ràng buộc với enrollments trước khi xóa

---

## 📖 QUẢN LÝ BÀI HỌC

### Danh sách bài học:
- **Đường dẫn:** `/Admin/Lessons`
- **Chức năng:**
  - Xem tất cả bài học theo khóa học
  - Tìm kiếm theo tên bài học
  - Lọc theo loại nội dung (Video, Text, Quiz)

### Tạo bài học mới:
- **Đường dẫn:** `/Admin/Lessons/Create`
- **Thông tin cần nhập:**
  - Tên bài học
  - Mô tả
  - Khóa học thuộc về
  - Chương (Chapter)
  - Loại nội dung
  - Thứ tự hiển thị
  - Nội dung chi tiết

### Quản lý nội dung:
- **Video:** Upload file hoặc nhúng YouTube/Vimeo
- **Text:** Soạn thảo nội dung với rich text editor
- **Quiz:** Tạo câu hỏi trắc nghiệm

---

## 👥 QUẢN LÝ NGƯỜI DÙNG

### Danh sách người dùng:
- **Đường dẫn:** `/Admin/Users`
- **Chức năng:**
  - Xem tất cả người dùng
  - Tìm kiếm theo email, tên
  - Lọc theo vai trò, trạng thái
  - Phân quyền người dùng

### Quản lý vai trò:
- **Admin:** Toàn quyền quản trị
- **User:** Người dùng thường
- **Instructor:** Giảng viên (nếu có)

### Thao tác với người dùng:
- Xem chi tiết thông tin
- Chỉnh sửa thông tin
- Khóa/mở khóa tài khoản
- Phân quyền vai trò

---

## 📊 QUẢN LÝ ĐĂNG KÝ HỌC

### Danh sách đăng ký:
- **Đường dẫn:** `/Admin/Enrollments`
- **Chức năng:**
  - Xem tất cả đăng ký học
  - Tìm kiếm theo người dùng, khóa học
  - Lọc theo trạng thái, ngày đăng ký
  - Theo dõi tiến độ học tập

### Trạng thái đăng ký:
- **Pending:** Chờ xác nhận
- **Active:** Đang học
- **Completed:** Hoàn thành
- **Cancelled:** Đã hủy

### Thao tác quản lý:
- Xác nhận đăng ký
- Hủy đăng ký
- Cập nhật tiến độ
- Xuất báo cáo

---

## 📈 QUẢN LÝ TIẾN độ HỌC TẬP

### Danh sách tiến độ:
- **Đường dẫn:** `/Admin/LessonProgresses`
- **Chức năng:**
  - Xem tiến độ học tập của tất cả học viên
  - Tìm kiếm theo User ID, trạng thái
  - Lọc theo bài học, mức độ hoàn thành
  - Cập nhật tiến độ trực tiếp

### Cập nhật tiến độ:
- **Cách 1:** Click nút edit trong danh sách
- **Cách 2:** Sử dụng modal popup cập nhật nhanh
- **Cách 3:** Bulk update cho nhiều tiến độ cùng lúc

### Thống kê tiến độ:
- **Đường dẫn:** `/Admin/LessonProgresses/Statistics`
- **Nội dung:**
  - Tổng quan tiến độ học tập
  - Biểu đồ hoàn thành theo thời gian
  - Top bài học được học nhiều nhất
  - Thống kê theo ngày/tuần/tháng

---

## 💬 QUẢN LÝ BÌNH LUẬN

### Danh sách bình luận:
- **Đường dẫn:** `/Admin/Comments`
- **Chức năng:**
  - Xem tất cả bình luận
  - Tìm kiếm theo nội dung, người dùng
  - Lọc theo khóa học, trạng thái
  - Duyệt/ẩn bình luận

### Moderation:
- Duyệt bình luận mới
- Ẩn bình luận không phù hợp
- Trả lời bình luận học viên
- Xóa bình luận spam

---

## 🎫 QUẢN LÝ KHUYẾN MÃI

### Danh sách khuyến mãi:
- **Đường dẫn:** `/Admin/Promotions`
- **Chức năng:**
  - Tạo mã giảm giá
  - Thiết lập % hoặc số tiền giảm
  - Đặt thời hạn sử dụng
  - Giới hạn số lần sử dụng

### Loại khuyến mãi:
- **Percentage:** Giảm theo phần trăm
- **Fixed Amount:** Giảm số tiền cố định
- **Free Shipping:** Miễn phí (nếu có)

---

## 🏆 QUẢN LÝ CHỨNG CHỈ

### Danh sách chứng chỉ:
- **Đường dẫn:** `/Admin/Certificates`
- **Chức năng:**
  - Xem chứng chỉ đã cấp
  - Tìm kiếm theo người dùng, khóa học
  - Tạo chứng chỉ mới
  - In/xuất chứng chỉ PDF

### Tự động cấp chứng chỉ:
- Khi học viên hoàn thành 100% khóa học
- Đạt điểm tối thiểu (nếu có quiz)
- Thời gian học đủ yêu cầu

---

## 🔧 CÀI ĐẶT HỆ THỐNG

### Cấu hình database:
- File: `appsettings.json`
- Connection string SQL Server
- Chạy migrations: `dotnet ef database update`

### Cấu hình email:
- SMTP settings cho gửi email xác nhận
- Email templates cho thông báo

### Cấu hình file upload:
- Thư mục lưu trữ: `wwwroot/uploads`
- Giới hạn kích thước file
- Định dạng file cho phép

---

## 🚨 XỬ LÝ SỰ CỐ

### Lỗi thường gặp:
1. **Không đăng nhập được:**
   - Kiểm tra email/password
   - Xóa cache browser
   - Kiểm tra database connection

2. **Không upload được file:**
   - Kiểm tra quyền thư mục
   - Kiểm tra kích thước file
   - Kiểm tra định dạng file

3. **Lỗi database:**
   - Chạy lại migrations
   - Kiểm tra connection string
   - Backup và restore database

### Liên hệ hỗ trợ:
- Email: <EMAIL>
- Hotline: 1900-xxxx
- Documentation: /docs

---

## 📝 GHI CHÚ QUAN TRỌNG

1. **Backup định kỳ:** Sao lưu database và files thường xuyên
2. **Bảo mật:** Thay đổi password mặc định ngay lập tức
3. **Cập nhật:** Kiểm tra và cập nhật hệ thống định kỳ
4. **Monitor:** Theo dõi logs và performance

**Phiên bản:** 1.0.0  
**Cập nhật lần cuối:** 2024-12-19
