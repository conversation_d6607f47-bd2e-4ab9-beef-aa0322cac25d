@page
@model ChangePasswordModel
@{
    ViewData["Title"] = "Đổi mật khẩu";
    ViewData["ActivePage"] = "ChangePassword";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <partial name="_ManageNav" />
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        <PERSON><PERSON><PERSON> mật khẩu
                    </h4>
                </div>
                <div class="card-body">
                    <partial name="_StatusMessage" for="StatusMessage" />
                    
                    <form id="change-password-form" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.OldPassword" class="form-label">M<PERSON><PERSON> khẩu hiện tại</label>
                            <input asp-for="Input.OldPassword" class="form-control" type="password" />
                            <span asp-validation-for="Input.OldPassword" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.NewPassword" class="form-label">Mật khẩu mới</label>
                            <input asp-for="Input.NewPassword" class="form-control" type="password" />
                            <span asp-validation-for="Input.NewPassword" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.ConfirmPassword" class="form-label">Xác nhận mật khẩu mới</label>
                            <input asp-for="Input.ConfirmPassword" class="form-control" type="password" />
                            <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Cập nhật mật khẩu
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
