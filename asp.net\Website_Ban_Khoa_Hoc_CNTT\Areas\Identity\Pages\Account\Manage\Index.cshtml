@page
@model ELearningWebsite.Areas.Identity.Pages.Account.Manage.IndexModel
@{
    ViewData["Title"] = "Quản lý tài khoản";
    ViewData["ActivePage"] = "Index";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <partial name="_ManageNav" />
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Thông tin tài khoản
                    </h4>
                </div>
                <div class="card-body">
                    <partial name="_StatusMessage" for="StatusMessage" />
                    
                    <form id="profile-form" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Username" class="form-label">Tên đăng nhập</label>
                            <input asp-for="Username" class="form-control" disabled />
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                            @if (Model.IsEmailConfirmed)
                            {
                                <div class="input-group-append">
                                    <span class="h-100 input-group-text text-success font-weight-bold">✓</span>
                                </div>
                            }
                            else
                            {
                                <button id="email-verification" type="submit" asp-page-handler="SendVerificationEmail" class="btn btn-link" disabled>Gửi email xác thực</button>
                            }
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.FullName" class="form-label">Họ và tên</label>
                            <input asp-for="Input.FullName" class="form-control" />
                            <span asp-validation-for="Input.FullName" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.PhoneNumber" class="form-label">Số điện thoại</label>
                            <input asp-for="Input.PhoneNumber" class="form-control" />
                            <span asp-validation-for="Input.PhoneNumber" class="text-danger"></span>
                        </div>
                        
                        <button id="update-profile-button" type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Lưu thay đổi
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
