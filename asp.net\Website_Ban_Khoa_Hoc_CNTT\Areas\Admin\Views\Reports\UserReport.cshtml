@model UserReportViewModel
@{
    ViewData["Title"] = "Báo cáo Ng<PERSON>i dùng";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-users me-2"></i>
            B<PERSON>o c<PERSON>o <PERSON>ờ<PERSON> dùng
        </h2>
        <div>
            <button class="admin-btn admin-btn-primary" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>
                Xuất Excel
            </button>
        </div>
    </div>

    <!-- User Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-3">
            <div class="admin-card stat-card-primary">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng Users</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalUsers</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-3">
            <div class="admin-card stat-card-success">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Users Đã Xác thực</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.VerifiedUsers</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-3">
            <div class="admin-card stat-card-warning">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Users Chưa Xác thực</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.UnverifiedUsers</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-user-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Registration Trend Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Xu hướng Đăng ký (12 tháng gần đây)</h5>
                </div>
                <div class="admin-card-body">
                    <canvas id="registrationTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Users Gần đây</h5>
                </div>
                <div class="admin-card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Họ tên</th>
                                    <th>Email</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model.RecentUsers)
                                {
                                    <tr>
                                        <td>@user.FullName</td>
                                        <td>@user.Email</td>
                                        <td>
                                            @if (user.IsVerified)
                                            {
                                                <span class="badge bg-success">Đã xác thực</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">Chưa xác thực</span>
                                            }
                                        </td>
                                        <td>@user.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td>
                                            <a href="/Admin/Users/<USER>/@user.Id" class="btn btn-sm btn-info">
                                                <i class="fas fa-info-circle"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Registration Trend Chart
        var ctx = document.getElementById('registrationTrendChart').getContext('2d');
        var data = {
            labels: [@Html.Raw(string.Join(",", Model.RegistrationTrend.Select(x => $"'{x.Month}'")))],
            datasets: [{
                label: 'Users mới',
                data: [@string.Join(",", Model.RegistrationTrend.Select(x => x.NewUsers))],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
        var options = {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };
        new Chart(ctx, {
            type: 'line',
            data: data,
            options: options
        });

        // Export function
        function exportToExcel() {
            fetch('/Admin/Reports/ExportToExcel?reportType=user', {
                method: 'POST'
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Có lỗi xảy ra khi xuất Excel');
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'BaoCaoNguoiDung.xlsx';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            })
            .catch(error => {
                alert(error.message);
            });
        }
    </script>
} 