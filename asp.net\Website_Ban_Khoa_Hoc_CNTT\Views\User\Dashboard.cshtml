@model UserDashboardViewModel
@{
    ViewData["Title"] = "Dashboard - " + Model.User.FullName;
}

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Chào mừng trở lại, @Model.User.FullName!</h2>
                    <p class="text-muted mb-0">Theo dõi tiến độ học tập của bạn</p>
                </div>
                <div>
                    <a href="@Url.Action("Courses", "Home")" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tìm khóa học mới
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalCourses</h4>
                            <p class="mb-0">Tổng khóa học</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-book fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.CompletedCourses</h4>
                            <p class="mb-0">Đã hoàn thành</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.InProgressCourses</h4>
                            <p class="mb-0">Đang học</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.AverageProgress.ToString("F1")%</h4>
                            <p class="mb-0">Tiến độ trung bình</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Courses -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Khóa học gần đây</h5>
                    <a href="@Url.Action("MyCourses", "User")" class="btn btn-sm btn-outline-primary">
                        Xem tất cả
                    </a>
                </div>
                <div class="card-body">
                    @if (Model.RecentEnrollments.Any())
                    {
                        @foreach (var enrollment in Model.RecentEnrollments)
                        {
                            <div class="d-flex align-items-center mb-3 pb-3 @(enrollment != Model.RecentEnrollments.Last() ? "border-bottom" : "")">
                                <img src="@enrollment.Course.Thumbnail" alt="@enrollment.Course.Title" 
                                     class="rounded me-3" style="width: 60px; height: 40px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="@Url.Action("CourseProgress", "User", new { id = enrollment.Id })" 
                                           class="text-decoration-none">@enrollment.Course.Title</a>
                                    </h6>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="progress flex-grow-1 me-3" style="height: 6px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: @enrollment.Progress%" 
                                                 aria-valuenow="@enrollment.Progress" 
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted">@enrollment.Progress.ToString("F0")%</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            @enrollment.EnrollmentDate.ToString("dd/MM/yyyy")
                                        </small>
                                        <span class="badge bg-@(enrollment.Status == 1 ? "primary" : enrollment.Status == 3 ? "success" : "warning")">
                                            @enrollment.GetStatusText()
                                        </span>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Chưa có khóa học nào</h6>
                            <p class="text-muted">Hãy đăng ký khóa học đầu tiên của bạn!</p>
                            <a href="@Url.Action("Courses", "Home")" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Tìm khóa học
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Quick Actions & Profile -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Thao tác nhanh</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="@Url.Action("MyCourses", "User")" class="btn btn-outline-primary">
                            <i class="fas fa-book me-2"></i>Khóa học của tôi
                        </a>
                        <a href="@Url.Action("Courses", "Home")" class="btn btn-outline-success">
                            <i class="fas fa-search me-2"></i>Tìm khóa học mới
                        </a>
                        <a href="/Identity/Account/Manage" class="btn btn-outline-info">
                            <i class="fas fa-user-cog me-2"></i>Cài đặt tài khoản
                        </a>
                    </div>
                </div>
            </div>

            <!-- Profile Summary -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Thông tin cá nhân</h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        @if (!string.IsNullOrEmpty(Model.User.Avatar))
                        {
                            <img src="@Model.User.Avatar" alt="Avatar" 
                                 class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;">
                        }
                        else
                        {
                            <div class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" 
                                 style="width: 80px; height: 80px; font-size: 2rem;">
                                @Model.User.FullName.FirstOrDefault()
                            </div>
                        }
                    </div>
                    <h6 class="mb-1">@Model.User.FullName</h6>
                    <p class="text-muted mb-2">@Model.User.Email</p>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Tham gia từ @Model.User.CreatedAt.ToString("MM/yyyy")
                    </small>
                    
                    @if (Model.User.IsVerified)
                    {
                        <div class="mt-2">
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Đã xác thực
                            </span>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
        transition: transform 0.2s ease-in-out;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .opacity-75 {
        opacity: 0.75;
    }
    
    .bg-primary { background-color: #0d6efd !important; }
    .bg-success { background-color: #198754 !important; }
    .bg-warning { background-color: #ffc107 !important; }
    .bg-info { background-color: #0dcaf0 !important; }
</style>

<script>
    // Add animation to statistics cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
