@model ELearningWebsite.Areas.Admin.ViewModels.CommentEditViewModel
@{
    ViewData["Title"] = "Chỉnh sửa bình luận";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-edit text-warning me-2"></i>
                Chỉnh sửa bình luận #@Model.Id
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Index")">Bình luận</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Details", new { id = Model.Id })">Chi tiết</a></li>
                    <li class="breadcrumb-item active">Chỉnh sửa</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Edit Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Chỉnh sửa nội dung
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="LessonId" type="hidden" />
                        <input asp-for="UserId" type="hidden" />
                        <input asp-for="ParentCommentId" type="hidden" />
                        <input asp-for="CreatedAt" type="hidden" />
                        <input asp-for="UpdatedAt" type="hidden" />
                        <input asp-for="IsDelete" type="hidden" />

                        <!-- Parent Comment (if this is a reply) -->
                        @if (Model.ParentCommentId.HasValue)
                        {
                            <div class="alert alert-info mb-4">
                                <h6 class="alert-heading">
                                    <i class="fas fa-reply me-1"></i>
                                    Đây là phản hồi cho bình luận
                                </h6>
                                <p class="mb-0">@Model.ParentCommentContent</p>
                            </div>
                        }

                        <!-- Content Field -->
                        <div class="mb-4">
                            <label asp-for="Content" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                Nội dung bình luận <span class="text-danger">*</span>
                            </label>
                            <textarea asp-for="Content" class="form-control" rows="6" 
                                      placeholder="Nhập nội dung bình luận..."></textarea>
                            <span asp-validation-for="Content" class="text-danger"></span>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Tối đa 2000 ký tự. Hiện tại: <span id="char-count">0</span> ký tự
                            </div>
                        </div>

                        <!-- Preview -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-eye me-1"></i>
                                Xem trước
                            </label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <img src="/images/default-avatar.png" alt="Avatar" class="rounded-circle me-3" width="40" height="40">
                                        <div>
                                            <h6 class="mb-0">@Model.UserName</h6>
                                            <small class="text-muted">Bây giờ</small>
                                        </div>
                                    </div>
                                    <div id="preview-content" class="comment-preview">
                                        @Model.Content
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>
                                Lưu thay đổi
                            </button>
                            <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                Hủy bỏ
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Comment Info -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Thông tin bình luận
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">ID:</label>
                        <div>
                            <span class="badge bg-secondary">#@Model.Id</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Bài học:</label>
                        <div>
                            <strong>@Model.LessonTitle</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Người dùng:</label>
                        <div>
                            <strong>@Model.UserName</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Ngày tạo:</label>
                        <div>
                            @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                        </div>
                    </div>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <div class="mb-0">
                            <label class="form-label text-muted">Cập nhật lần cuối:</label>
                            <div>
                                @Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Guidelines -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Hướng dẫn chỉnh sửa
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Giữ nguyên ý nghĩa gốc của bình luận
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Sửa lỗi chính tả và ngữ pháp
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Loại bỏ nội dung không phù hợp
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            Không thay đổi hoàn toàn nội dung
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-times text-danger me-2"></i>
                            Không xóa thông tin quan trọng
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Warning -->
            <div class="card mt-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Lưu ý quan trọng
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">
                        <small>
                            Việc chỉnh sửa bình luận sẽ được ghi lại trong hệ thống. 
                            Hãy đảm bảo rằng các thay đổi là cần thiết và phù hợp.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .comment-preview {
            min-height: 60px;
            line-height: 1.6;
            word-wrap: break-word;
        }
        
        .comment-preview:empty::before {
            content: "Xem trước sẽ hiển thị ở đây...";
            color: #6c757d;
            font-style: italic;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            var contentTextarea = $('#Content');
            var charCount = $('#char-count');
            var previewContent = $('#preview-content');
            
            // Update character count and preview
            function updateCountAndPreview() {
                var content = contentTextarea.val();
                charCount.text(content.length);
                
                // Update preview
                if (content.trim()) {
                    previewContent.text(content);
                } else {
                    previewContent.empty();
                }
                
                // Update character count color
                if (content.length > 2000) {
                    charCount.addClass('text-danger').removeClass('text-muted');
                } else if (content.length > 1800) {
                    charCount.addClass('text-warning').removeClass('text-muted text-danger');
                } else {
                    charCount.addClass('text-muted').removeClass('text-warning text-danger');
                }
            }
            
            // Initial update
            updateCountAndPreview();
            
            // Update on input
            contentTextarea.on('input', updateCountAndPreview);
            
            // Auto-resize textarea
            contentTextarea.on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
            
            // Form validation
            $('form').on('submit', function(e) {
                var content = contentTextarea.val().trim();
                
                if (!content) {
                    e.preventDefault();
                    toastr.error('Vui lòng nhập nội dung bình luận');
                    contentTextarea.focus();
                    return false;
                }
                
                if (content.length > 2000) {
                    e.preventDefault();
                    toastr.error('Nội dung bình luận không được vượt quá 2000 ký tự');
                    contentTextarea.focus();
                    return false;
                }
            });
        });
    </script>
}
