@page
@model LoginModel

@{
    ViewData["Title"] = "Đăng nhập";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i><PERSON>ăng nhập
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form id="account" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="Nhập email của bạn" />
                            </div>
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.Password" class="form-label">Mật khẩu</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="Nhập mật khẩu" />
                            </div>
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            <label class="form-check-label" asp-for="Input.RememberMe">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button id="login-submit" type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </button>
                        </div>
                        
                        <div class="text-center mt-3">
                            <p class="mb-1">
                                <a id="forgot-password" asp-page="./ForgotPassword">Quên mật khẩu?</a>
                            </p>
                            <p class="mb-0">
                                Chưa có tài khoản? <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl">Đăng ký ngay</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
