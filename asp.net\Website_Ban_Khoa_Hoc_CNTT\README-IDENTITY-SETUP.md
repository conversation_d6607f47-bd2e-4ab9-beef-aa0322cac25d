# 🔐 Hướng dẫn thiết lập hệ thống đăng nhập và phân quyền

## ✅ Đã hoàn thành

### 1. T<PERSON><PERSON> các bảng ASP.NET Identity
Đã tạo thành công tất cả các bảng cần thiết cho hệ thống authentication và authorization:

- ✅ **AspNetUsers** - Bảng người dùng (với custom fields từ ApplicationUser)
- ✅ **AspNetRoles** - Bảng vai trò/quyền  
- ✅ **AspNetUserRoles** - Bảng liên kết user-role
- ✅ **AspNetUserClaims** - <PERSON><PERSON><PERSON> claims của user
- ✅ **AspNetRoleClaims** - <PERSON><PERSON><PERSON> claims của role
- ✅ **AspNetUserLogins** - Bảng đăng nhập external (Google, Facebook, etc.)
- ✅ **AspNetUserTokens** - Bảng tokens

### 2. T<PERSON>o dữ liệu mẫu
Đã tạo 3 roles c<PERSON> bản:
- ✅ **Admin** - Quản trị viên
- ✅ **Instructor** - Giảng viên  
- ✅ **Student** - <PERSON><PERSON><PERSON> viên

### 3. Tạo users mẫu
Seed data đã tạo các user mẫu với thông tin đăng nhập:

**Admin:**
- Email: `<EMAIL>`
- Password: `Admin@123`
- Role: Admin

**Instructor:**  
- Email: `<EMAIL>`
- Password: `Instructor@123`
- Role: Instructor

**Student:**
- Email: `<EMAIL>` 
- Password: `Student@123`
- Role: Student

### 4. Sửa lỗi và tối ưu
- ✅ Sửa conflict ErrorViewModel
- ✅ Tạo view Contact.cshtml
- ✅ Đồng bộ migration với database
- ✅ Kích hoạt seed data

## 🚀 Cách sử dụng

### Đăng nhập vào hệ thống
1. Truy cập: `http://localhost:5000/Identity/Account/Login`
2. Sử dụng một trong các tài khoản mẫu ở trên
3. Sau khi đăng nhập thành công, bạn sẽ được chuyển hướng về trang chủ

### Đăng ký tài khoản mới
1. Truy cập: `http://localhost:5000/Identity/Account/Register`
2. Điền thông tin đăng ký
3. Tài khoản mới sẽ có role mặc định là "Student"

### Quản lý phân quyền
Hệ thống đã sẵn sàng cho việc phân quyền theo roles:

```csharp
// Kiểm tra role trong Controller
[Authorize(Roles = "Admin")]
public IActionResult AdminOnly() { ... }

[Authorize(Roles = "Admin,Instructor")]  
public IActionResult AdminOrInstructor() { ... }

// Kiểm tra role trong View
@if (User.IsInRole("Admin"))
{
    <p>Chỉ Admin mới thấy nội dung này</p>
}
```

## 📁 Files đã tạo/sửa

### Models mới:
- `Models/Lesson.cs` - Model bài học
- `Models/Question.cs` - Model câu hỏi
- `Models/Payment.cs` - Model thanh toán

### Views mới:
- `Views/Home/Contact.cshtml` - Trang liên hệ

### Scripts:
- `create-identity-tables.sql` - Script tạo bảng Identity

### Migrations:
- `20250526084909_AddIdentityTablesSync` - Migration đồng bộ

## 🔧 Cấu hình hiện tại

### Connection String:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=DESKTOP-SODAP4H;Database=ECourseWeb;Integrated Security=true;TrustServerCertificate=true;"
  }
}
```

### Identity Configuration:
- Password requirements: Tối thiểu 6 ký tự, có chữ hoa, chữ thường, số
- Email confirmation: Không bắt buộc (cho development)
- Lockout: Kích hoạt sau 5 lần đăng nhập sai

## 🎯 Bước tiếp theo

### 1. Tùy chỉnh giao diện đăng nhập
- Customize các trang Identity trong `Areas/Identity/Pages/Account/`
- Thêm logo, màu sắc theo brand

### 2. Phân quyền chi tiết
- Thêm authorization cho các controller/action
- Tạo policy phức tạp hơn nếu cần

### 3. Tích hợp với business logic
- Liên kết User với Course enrollment
- Thêm profile management
- Tích hợp với payment system

## 🐛 Troubleshooting

### Lỗi "User already exists"
```bash
# Xóa user cũ nếu cần
DELETE FROM AspNetUserRoles WHERE UserId = 'user-id';
DELETE FROM AspNetUsers WHERE Email = '<EMAIL>';
```

### Reset Identity data
```bash
# Chạy script reset nếu cần
sqlcmd -S "DESKTOP-SODAP4H" -d "ECourseWeb" -E -Q "
DELETE FROM AspNetUserRoles;
DELETE FROM AspNetUsers;
DELETE FROM AspNetRoles;
"
```

### Kiểm tra roles
```sql
SELECT u.Email, u.FullName, r.Name as Role
FROM AspNetUsers u
JOIN AspNetUserRoles ur ON u.Id = ur.UserId  
JOIN AspNetRoles r ON ur.RoleId = r.Id
ORDER BY u.Email;
```

## 📞 Hỗ trợ
Nếu gặp vấn đề, hãy kiểm tra:
1. Database connection
2. Migration status: `dotnet ef migrations list`
3. Seed data: Kiểm tra bảng AspNetUsers và AspNetRoles
4. Application logs trong terminal

---
**Lưu ý:** Hệ thống đăng nhập và phân quyền đã hoàn tất và sẵn sàng sử dụng! 🎉
