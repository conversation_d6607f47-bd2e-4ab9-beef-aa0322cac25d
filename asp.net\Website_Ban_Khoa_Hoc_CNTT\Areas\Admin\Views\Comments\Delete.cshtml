@model ELearningWebsite.Areas.Admin.ViewModels.CommentDeleteViewModel
@{
    ViewData["Title"] = "Xóa bình luận";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-trash text-danger me-2"></i>
                Xóa bình luận #@Model.Id
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Index")">Bình luận</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Details", new { id = Model.Id })">Chi tiết</a></li>
                    <li class="breadcrumb-item active">Xóa</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Quay lại
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Alert -->
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Cảnh báo!
                </h4>
                <p>Bạn đang thực hiện xóa bình luận. Hành động này sẽ:</p>
                <ul class="mb-0">
                    <li>Ẩn bình luận khỏi giao diện người dùng</li>
                    @if (Model.HasReplies)
                    {
                        <li class="text-warning"><strong>Xóa tất cả @Model.RepliesCount phản hồi</strong></li>
                    }
                    <li>Có thể khôi phục lại sau này nếu cần</li>
                </ul>
            </div>

            <!-- Comment Preview -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comment me-2"></i>
                        Bình luận sẽ bị xóa
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Comment Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <img src="/images/default-avatar.png" alt="Avatar" class="rounded-circle me-3" width="50" height="50">
                                <div>
                                    <h6 class="mb-0">@Model.UserName</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="mb-2">
                                <span class="badge bg-info">@Model.LessonTitle</span>
                            </div>
                            @if (Model.HasReplies)
                            {
                                <div>
                                    <span class="badge bg-warning">@Model.RepliesCount phản hồi</span>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Comment Content -->
                    <div class="border-start border-3 border-primary ps-3">
                        <p class="mb-0">@Model.Content</p>
                    </div>
                </div>
            </div>

            <!-- Confirmation Form -->
            <div class="card mt-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Xác nhận xóa
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.HasReplies)
                    {
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Lưu ý đặc biệt
                            </h6>
                            <p class="mb-0">
                                Bình luận này có <strong>@Model.RepliesCount phản hồi</strong>. 
                                Khi xóa bình luận chính, tất cả các phản hồi cũng sẽ bị xóa theo.
                            </p>
                        </div>
                    }

                    <form asp-action="Delete" method="post">
                        <input asp-for="Id" type="hidden" />
                        
                        <div class="mb-4">
                            <p class="mb-3">
                                <strong>Bạn có chắc chắn muốn xóa bình luận này không?</strong>
                            </p>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    Tôi hiểu rằng hành động này sẽ xóa bình luận
                                    @if (Model.HasReplies)
                                    {
                                        <span>và tất cả @Model.RepliesCount phản hồi</span>
                                    }
                                </label>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                <i class="fas fa-trash me-1"></i>
                                Xác nhận xóa
                            </button>
                            <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                Hủy bỏ
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Information -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Thông tin thêm
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Xóa mềm (Soft Delete)</h6>
                            <p class="text-muted mb-0">
                                Bình luận sẽ được đánh dấu là đã xóa nhưng vẫn được lưu trong cơ sở dữ liệu. 
                                Bạn có thể khôi phục lại sau này nếu cần.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Khôi phục</h6>
                            <p class="text-muted mb-0">
                                Sau khi xóa, bạn có thể khôi phục bình luận thông qua trang chi tiết 
                                hoặc bộ lọc "Đã xóa" trong danh sách bình luận.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var confirmCheckbox = $('#confirmDelete');
            var deleteBtn = $('#deleteBtn');
            
            // Enable/disable delete button based on checkbox
            confirmCheckbox.on('change', function() {
                deleteBtn.prop('disabled', !this.checked);
            });
            
            // Form submission confirmation
            $('form').on('submit', function(e) {
                if (!confirmCheckbox.is(':checked')) {
                    e.preventDefault();
                    toastr.error('Vui lòng xác nhận bằng cách tích vào ô kiểm tra');
                    return false;
                }
                
                // Double confirmation for comments with replies
                @if (Model.HasReplies)
                {
                    <text>
                    if (!confirm('Bình luận này có @Model.RepliesCount phản hồi. Bạn có chắc chắn muốn xóa tất cả?')) {
                        e.preventDefault();
                        return false;
                    }
                    </text>
                }
                
                // Show loading state
                deleteBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Đang xóa...');
            });
        });
    </script>
}
