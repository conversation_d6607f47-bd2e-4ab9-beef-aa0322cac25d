<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Admin Panel</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Admin CSS -->
    <link href="~/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="admin-sidebar">
        <div class="p-3">
            <h4 class="text-white mb-4">
                <i class="fas fa-graduation-cap me-2"></i>
                Admin Panel
            </h4>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="/Admin">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/Admin/Courses">
                        <i class="fas fa-book"></i>
                        Khóa học
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/Admin/Categories">
                        <i class="fas fa-tags"></i>
                        Danh mục
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/Admin/Users">
                        <i class="fas fa-users"></i>
                        Người dùng
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/Admin/Enrollments">
                        <i class="fas fa-user-graduate"></i>
                        Đăng ký
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/Admin/Finances">
                        <i class="fas fa-chart-line"></i>
                        Tài chính
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/">
                        <i class="fas fa-home"></i>
                        Về trang chủ
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Header -->
        <div class="admin-header d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">@ViewData["Title"]</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                        <li class="breadcrumb-item active">@ViewData["Title"]</li>
                    </ol>
                </nav>
            </div>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i>
                    @User.Identity?.Name
                </button>
                <ul class="dropdown-menu admin-dropdown">
                    <li><a class="dropdown-item" href="/Identity/Account/Manage">
                        <i class="fas fa-cog me-2"></i>Cài đặt
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })" method="post">
                            <button type="submit" class="dropdown-item">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Page Content -->
        <div class="container-fluid">
            @RenderBody()
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Admin panel functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Set active nav item
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.admin-sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });

            // Mobile sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.querySelector('.admin-sidebar');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
