@model ELearningWebsite.Areas.Admin.ViewModels.ChapterDeleteViewModel
@{
    ViewData["Title"] = "Xóa Chương";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-trash text-danger"></i>
                <PERSON><PERSON><PERSON>
            </h1>
            <p class="admin-page-subtitle">Xác nhận xóa chương "@Model.Name"</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                <i class="fas fa-eye"></i> Xem chi tiết
            </a>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="admin-form-card">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    Xác nhận xóa chương
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <i class="fas fa-warning"></i> Cảnh báo!
                    </h6>
                    <p class="mb-0">
                        Bạn đang thực hiện xóa chương. Hành động này <strong>không thể hoàn tác</strong>.
                        @if (Model.HasLessons)
                        {
                            <br><strong>Chương này có @Model.LessonsCount bài học sẽ bị xóa cùng.</strong>
                        }
                    </p>
                </div>

                <!-- Chapter Information -->
                <div class="chapter-info">
                    <h6 class="section-title">
                        <i class="fas fa-list-ol text-primary"></i>
                        Thông tin chương sẽ bị xóa:
                    </h6>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <label>ID Chương:</label>
                            <span class="info-value">#@Model.Id</span>
                        </div>
                        <div class="info-item">
                            <label>Tên chương:</label>
                            <span class="info-value text-primary">@Model.Name</span>
                        </div>
                        <div class="info-item">
                            <label>Khóa học:</label>
                            <span class="info-value">@Model.CourseName</span>
                        </div>
                        <div class="info-item">
                            <label>Trạng thái:</label>
                            <span class="badge bg-@(Model.Status == "Active" ? "success" : "secondary")">
                                @Model.Status
                            </span>
                        </div>
                        <div class="info-item">
                            <label>Ngày tạo:</label>
                            <span class="info-value">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                        </div>
                        <div class="info-item">
                            <label>Số bài học:</label>
                            <span class="info-value text-warning">@Model.LessonsCount bài học</span>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div class="description-info mt-3">
                            <h6 class="section-title">
                                <i class="fas fa-align-left text-info"></i>
                                Mô tả:
                            </h6>
                            <div class="description-content">
                                @Html.Raw(Model.Description)
                            </div>
                        </div>
                    }
                </div>

                @if (Model.HasLessons)
                {
                    <!-- Lessons Information -->
                    <div class="lessons-info mt-4">
                        <h6 class="section-title">
                            <i class="fas fa-play-circle text-warning"></i>
                            Bài học sẽ bị xóa (@Model.LessonsCount):
                        </h6>
                        <div class="lessons-list">
                            @foreach (var lesson in Model.Lessons)
                            {
                                <div class="lesson-item">
                                    <div class="lesson-icon">
                                        <i class="fas fa-@(lesson.Type == "Video" ? "play" : lesson.Type == "Quiz" ? "question-circle" : "file-alt")"></i>
                                    </div>
                                    <div class="lesson-content">
                                        <div class="lesson-title">@lesson.Title</div>
                                        <div class="lesson-meta">
                                            <span class="badge bg-@(lesson.IsActive ? "success" : "secondary")">@lesson.Status</span>
                                            <span class="text-muted">@lesson.Type</span>
                                            @if (lesson.Duration > 0)
                                            {
                                                <span class="text-muted">@lesson.Duration phút</span>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Consequences -->
                <div class="consequences mt-4">
                    <h6 class="section-title">
                        <i class="fas fa-exclamation-circle text-warning"></i>
                        Hậu quả khi xóa:
                    </h6>
                    <ul class="consequence-list">
                        <li>Chương sẽ bị xóa vĩnh viễn khỏi hệ thống</li>
                        @if (Model.HasLessons)
                        {
                            <li>Tất cả @Model.LessonsCount bài học trong chương sẽ bị xóa</li>
                            <li>Tiến độ học tập của học viên sẽ bị ảnh hưởng</li>
                        }
                        <li>Cấu trúc khóa học sẽ thay đổi</li>
                        <li>Không thể khôi phục dữ liệu sau khi xóa</li>
                        <li>Thông tin chương sẽ không xuất hiện trong báo cáo</li>
                    </ul>
                </div>

                <!-- Alternative Actions -->
                <div class="alternatives mt-4">
                    <h6 class="section-title">
                        <i class="fas fa-lightbulb text-info"></i>
                        Thay vì xóa, bạn có thể:
                    </h6>
                    <div class="alternative-actions">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Chỉnh sửa thông tin
                        </a>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleStatus(@Model.Id)">
                            <i class="fas fa-@(Model.Status == "Active" ? "pause" : "play")"></i> 
                            @(Model.Status == "Active" ? "Vô hiệu hóa" : "Kích hoạt")
                        </button>
                    </div>
                </div>

                <!-- Confirmation Form -->
                <form asp-action="Delete" method="post" class="confirmation-form mt-4">
                    <input asp-for="Id" type="hidden">
                    
                    <div class="confirmation-checkbox">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                Tôi hiểu rằng hành động này không thể hoàn tác và xác nhận muốn xóa chương này
                                @if (Model.HasLessons)
                                {
                                    <strong>cùng với @Model.LessonsCount bài học</strong>
                                }
                            </label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy bỏ
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Enable delete button only when checkbox is checked
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('deleteButton').disabled = !this.checked;
});

function toggleStatus(chapterId) {
    if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái chương này thay vì xóa?')) {
        fetch(`/Admin/Chapters/ToggleStatus/${chapterId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => {
                    window.location.href = `/Admin/Chapters/Details/${chapterId}`;
                }, 1500);
            } else {
                toastr.error(data.message);
            }
        })
        .catch(error => {
            toastr.error('Có lỗi xảy ra khi thay đổi trạng thái');
        });
    }
}
</script>

<style>
.section-title {
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 500;
    color: #495057;
}

.description-content {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 4px solid #007bff;
}

.lessons-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
}

.lesson-item {
    display: flex;
    align-items-center;
    padding: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
    background-color: #fff;
    margin-bottom: 0.5rem;
    border-radius: 0.25rem;
}

.lesson-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.lesson-icon {
    margin-right: 0.75rem;
    color: #6c757d;
}

.lesson-content {
    flex: 1;
}

.lesson-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.lesson-meta {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.lesson-meta .badge {
    font-size: 0.7rem;
}

.consequence-list {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.consequence-list li {
    margin-bottom: 0.5rem;
    color: #dc3545;
}

.alternative-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.confirmation-form {
    border-top: 2px solid #dee2e6;
    padding-top: 1.5rem;
}

.confirmation-checkbox {
    margin-bottom: 1.5rem;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}
</style>
