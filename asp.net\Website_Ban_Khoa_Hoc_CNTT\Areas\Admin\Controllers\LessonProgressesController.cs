using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ELearningWebsite.Data;
using ELearningWebsite.Models;
using ELearningWebsite.Areas.Admin.ViewModels;
using Microsoft.AspNetCore.Authorization;

namespace ELearningWebsite.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class LessonProgressesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public LessonProgressesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Admin/LessonProgresses
        public async Task<IActionResult> Index(int page = 1, string searchTerm = "", int? lessonId = null,
            int? userId = null, string status = "", float? minProgress = null, float? maxProgress = null)
        {
            var viewModel = new LessonProgressIndexViewModel
            {
                CurrentPage = page,
                SearchTerm = searchTerm,
                LessonId = lessonId,
                UserId = userId,
                Status = status,
                MinProgress = minProgress,
                MaxProgress = maxProgress
            };

            // Build query
            var query = _context.LessonProgresses.AsQueryable();

            // Apply search filter
            if (!string.IsNullOrEmpty(searchTerm))
            {
                // Try to parse searchTerm as int for UserId search
                if (int.TryParse(searchTerm, out int userIdSearch))
                {
                    query = query.Where(lp => lp.UserId == userIdSearch ||
                                             (lp.Status != null && lp.Status.Contains(searchTerm)));
                }
                else
                {
                    query = query.Where(lp => (lp.Status != null && lp.Status.Contains(searchTerm)));
                }
            }

            // Apply lesson filter
            if (lessonId.HasValue)
            {
                query = query.Where(lp => lp.LessonId == lessonId.Value);
            }

            // Apply user filter
            if (userId.HasValue)
            {
                query = query.Where(lp => lp.UserId == userId.Value);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(status))
            {
                switch (status.ToLower())
                {
                    case "completed":
                        query = query.Where(lp => lp.ProgressPercentage >= 100);
                        break;
                    case "in-progress":
                        query = query.Where(lp => lp.ProgressPercentage > 0 && lp.ProgressPercentage < 100);
                        break;
                    case "not-started":
                        query = query.Where(lp => lp.ProgressPercentage == 0);
                        break;
                }
            }

            // Apply progress range filter
            if (minProgress.HasValue)
            {
                query = query.Where(lp => lp.ProgressPercentage >= minProgress.Value);
            }
            if (maxProgress.HasValue)
            {
                query = query.Where(lp => lp.ProgressPercentage <= maxProgress.Value);
            }

            // Get total count
            viewModel.TotalItems = await query.CountAsync();
            viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.TotalItems / viewModel.PageSize);

            // Get paginated results
            var rawProgresses = await query
                .OrderByDescending(lp => lp.UpdatedAt ?? lp.CreatedAt)
                .Skip((page - 1) * viewModel.PageSize)
                .Take(viewModel.PageSize)
                .ToListAsync();

            // Map to view model after getting data from database
            var progresses = rawProgresses.Select(lp => new LessonProgressListItem
            {
                Id = lp.Id,
                LessonId = lp.LessonId,
                LessonTitle = "Lesson " + lp.LessonId.ToString(), // Simplified since we don't have Lesson table
                CourseTitle = "Course Title", // Simplified
                ChapterName = "Chapter Name", // Simplified
                UserId = lp.UserId,
                UserName = "User " + lp.UserId, // Simplified
                UserEmail = lp.UserId + "@example.com", // Simplified
                ProgressPercentage = lp.ProgressPercentage,
                TimeSpent = lp.TimeSpent,
                CreatedAt = lp.CreatedAt,
                UpdatedAt = lp.UpdatedAt,
                Status = lp.Status,
                Passing = lp.Passing,
                CountDoing = lp.CountDoing,
                HighestMark = lp.HighestMark
            }).ToList();

            viewModel.LessonProgresses = progresses;

            // Load filter options
            await LoadFilterOptions(viewModel);

            return View(viewModel);
        }

        // GET: Admin/LessonProgresses/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var progress = await _context.LessonProgresses
                .FirstOrDefaultAsync(lp => lp.Id == id);

            if (progress == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy tiến độ học tập";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new LessonProgressDetailsViewModel
            {
                Id = progress.Id,
                LessonId = progress.LessonId,
                LessonTitle = "Lesson " + progress.LessonId.ToString(), // Simplified
                LessonDescription = "Lesson description", // Simplified
                LessonType = "Video", // Simplified
                CourseTitle = "Course Title", // Simplified
                ChapterName = "Chapter Name", // Simplified
                UserId = progress.UserId,
                UserName = "User " + progress.UserId, // Simplified
                UserEmail = progress.UserId + "@example.com", // Simplified
                ProgressPercentage = progress.ProgressPercentage,
                TimeSpent = progress.TimeSpent,
                CreatedAt = progress.CreatedAt,
                UpdatedAt = progress.UpdatedAt,
                Status = progress.Status,
                Passing = progress.Passing,
                CountDoing = progress.CountDoing,
                HighestMark = progress.HighestMark,
                ProgressHistory = new List<ProgressHistoryItem>
                {
                    new ProgressHistoryItem
                    {
                        Date = progress.CreatedAt,
                        ProgressPercentage = 0,
                        Action = "Started",
                        Notes = "Bắt đầu học bài"
                    }
                }
            };

            if (progress.UpdatedAt.HasValue && progress.ProgressPercentage > 0)
            {
                viewModel.ProgressHistory.Add(new ProgressHistoryItem
                {
                    Date = progress.UpdatedAt.Value,
                    ProgressPercentage = progress.ProgressPercentage,
                    TimeSpent = progress.TimeSpent,
                    Action = progress.ProgressPercentage >= 100 ? "Completed" : "Updated",
                    Notes = progress.ProgressPercentage >= 100 ? "Hoàn thành bài học" : "Cập nhật tiến độ"
                });
            }

            return View(viewModel);
        }

        // GET: Admin/LessonProgresses/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new LessonProgressCreateViewModel();
            await LoadCreateEditOptions(viewModel);
            return View(viewModel);
        }

        // POST: Admin/LessonProgresses/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(LessonProgressCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Check if progress already exists for this user and lesson
                    var existingProgress = await _context.LessonProgresses
                        .AnyAsync(lp => lp.LessonId == model.LessonId && lp.UserId == model.UserId);

                    if (existingProgress)
                    {
                        TempData["ErrorMessage"] = "Tiến độ học tập cho học viên này và bài học này đã tồn tại";
                        await LoadCreateEditOptions(model);
                        return View(model);
                    }

                    var progress = new LessonProgress
                    {
                        LessonId = model.LessonId,
                        UserId = model.UserId,
                        ProgressPercentage = model.ProgressPercentage,
                        TimeSpent = model.TimeSpent,
                        Status = model.Status,
                        Passing = model.Passing,
                        CountDoing = model.CountDoing,
                        HighestMark = model.HighestMark,
                        CreatedAt = DateTime.Now
                    };

                    _context.LessonProgresses.Add(progress);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "Tạo tiến độ học tập thành công";
                    return RedirectToAction(nameof(Details), new { id = progress.Id });
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }

            await LoadCreateEditOptions(model);
            return View(model);
        }

        // GET: Admin/LessonProgresses/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var progress = await _context.LessonProgresses
                .FirstOrDefaultAsync(lp => lp.Id == id);

            if (progress == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy tiến độ học tập";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new LessonProgressEditViewModel
            {
                Id = progress.Id,
                LessonId = progress.LessonId,
                UserId = progress.UserId,
                ProgressPercentage = progress.ProgressPercentage,
                TimeSpent = progress.TimeSpent,
                Status = progress.Status,
                Passing = progress.Passing,
                CountDoing = progress.CountDoing,
                HighestMark = progress.HighestMark,
                LessonTitle = "Lesson " + progress.LessonId.ToString(), // Simplified
                UserName = "User " + progress.UserId, // Simplified
                CreatedAt = progress.CreatedAt
            };

            await LoadCreateEditOptions(viewModel);
            return View(viewModel);
        }

        // POST: Admin/LessonProgresses/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, LessonProgressEditViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var progress = await _context.LessonProgresses.FindAsync(id);
                    if (progress == null)
                    {
                        TempData["ErrorMessage"] = "Không tìm thấy tiến độ học tập";
                        return RedirectToAction(nameof(Index));
                    }

                    // Check if changing lesson/user would create duplicate
                    if (progress.LessonId != model.LessonId || progress.UserId != model.UserId)
                    {
                        var existingProgress = await _context.LessonProgresses
                            .AnyAsync(lp => lp.Id != id && lp.LessonId == model.LessonId && lp.UserId == model.UserId);

                        if (existingProgress)
                        {
                            TempData["ErrorMessage"] = "Tiến độ học tập cho học viên này và bài học này đã tồn tại";
                            await LoadCreateEditOptions(model);
                            return View(model);
                        }
                    }

                    progress.LessonId = model.LessonId;
                    progress.UserId = model.UserId;
                    progress.ProgressPercentage = model.ProgressPercentage;
                    progress.TimeSpent = model.TimeSpent;
                    progress.Status = model.Status;
                    progress.Passing = model.Passing;
                    progress.CountDoing = model.CountDoing;
                    progress.HighestMark = model.HighestMark;
                    progress.UpdatedAt = DateTime.Now;

                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "Cập nhật tiến độ học tập thành công";
                    return RedirectToAction(nameof(Details), new { id = progress.Id });
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                }
            }

            // Reload data if validation fails
            var originalProgress = await _context.LessonProgresses.FindAsync(id);
            if (originalProgress != null)
            {
                model.LessonTitle = "Lesson " + originalProgress.LessonId.ToString();
                model.UserName = "User " + originalProgress.UserId;
                model.CreatedAt = originalProgress.CreatedAt;
            }

            await LoadCreateEditOptions(model);
            return View(model);
        }

        // GET: Admin/LessonProgresses/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var progress = await _context.LessonProgresses
                .FirstOrDefaultAsync(lp => lp.Id == id);

            if (progress == null)
            {
                TempData["ErrorMessage"] = "Không tìm thấy tiến độ học tập";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new LessonProgressDeleteViewModel
            {
                Id = progress.Id,
                LessonTitle = "Lesson " + progress.LessonId.ToString(), // Simplified
                CourseTitle = "Course Title", // Simplified
                ChapterName = "Chapter Name", // Simplified
                UserName = "User " + progress.UserId, // Simplified
                UserEmail = progress.UserId + "@example.com", // Simplified
                ProgressPercentage = progress.ProgressPercentage,
                TimeSpent = progress.TimeSpent,
                CreatedAt = progress.CreatedAt,
                Status = progress.Status,
                CountDoing = progress.CountDoing,
                HighestMark = progress.HighestMark
            };

            return View(viewModel);
        }

        // POST: Admin/LessonProgresses/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var progress = await _context.LessonProgresses.FindAsync(id);
                if (progress == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy tiến độ học tập";
                    return RedirectToAction(nameof(Index));
                }

                _context.LessonProgresses.Remove(progress);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Xóa tiến độ học tập thành công";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Có lỗi xảy ra: " + ex.Message;
                return RedirectToAction(nameof(Delete), new { id });
            }
        }

        private async Task LoadFilterOptions(LessonProgressIndexViewModel viewModel)
        {
            // Load available lessons (simplified)
            viewModel.AvailableLessons = Enumerable.Range(1, 50)
                .Select(i => new ProgressLessonOption
                {
                    Id = i,
                    Title = $"Lesson {i}",
                    CourseTitle = $"Course {(i - 1) / 10 + 1}",
                    ChapterName = $"Chapter {(i - 1) / 5 + 1}",
                    Type = "Video",
                    LearnerCount = 0
                })
                .ToList();

            // Load available users (simplified)
            var userIds = await _context.LessonProgresses
                .Select(lp => lp.UserId)
                .Distinct()
                .Take(20)
                .ToListAsync();

            viewModel.AvailableUsers = userIds
                .Select(userId => new ProgressUserOption
                {
                    Id = userId,
                    Name = "User " + userId,
                    Email = userId + "@example.com",
                    EnrollmentCount = 0,
                    CompletedLessons = 0
                })
                .ToList();
        }

        private async Task LoadCreateEditOptions(LessonProgressCreateViewModel viewModel)
        {
            // Load available lessons (simplified)
            viewModel.AvailableLessons = Enumerable.Range(1, 50)
                .Select(i => new ProgressLessonOption
                {
                    Id = i,
                    Title = $"Lesson {i}",
                    CourseTitle = $"Course {(i - 1) / 10 + 1}",
                    ChapterName = $"Chapter {(i - 1) / 5 + 1}",
                    Type = "Video",
                    LearnerCount = 0
                })
                .ToList();

            // Load available users (simplified)
            var userIds = await _context.LessonProgresses
                .Select(lp => lp.UserId)
                .Distinct()
                .Take(20)
                .ToListAsync();

            viewModel.AvailableUsers = userIds
                .Select(userId => new ProgressUserOption
                {
                    Id = userId,
                    Name = "User " + userId,
                    Email = userId + "@example.com",
                    EnrollmentCount = 0,
                    CompletedLessons = 0
                })
                .ToList();

            // Add some default users if none exist
            if (!viewModel.AvailableUsers.Any())
            {
                viewModel.AvailableUsers = Enumerable.Range(1, 10)
                    .Select(i => new ProgressUserOption
                    {
                        Id = i,
                        Name = $"User {i}",
                        Email = $"user{i}@example.com",
                        EnrollmentCount = 0,
                        CompletedLessons = 0
                    })
                    .ToList();
            }
        }

        private async Task LoadCreateEditOptions(LessonProgressEditViewModel viewModel)
        {
            // Load available lessons (simplified)
            viewModel.AvailableLessons = Enumerable.Range(1, 50)
                .Select(i => new ProgressLessonOption
                {
                    Id = i,
                    Title = $"Lesson {i}",
                    CourseTitle = $"Course {(i - 1) / 10 + 1}",
                    ChapterName = $"Chapter {(i - 1) / 5 + 1}",
                    Type = "Video",
                    LearnerCount = 0
                })
                .ToList();

            // Load available users (simplified)
            var userIds = await _context.LessonProgresses
                .Select(lp => lp.UserId)
                .Distinct()
                .Take(20)
                .ToListAsync();

            viewModel.AvailableUsers = userIds
                .Select(userId => new ProgressUserOption
                {
                    Id = userId,
                    Name = "User " + userId,
                    Email = userId + "@example.com",
                    EnrollmentCount = 0,
                    CompletedLessons = 0
                })
                .ToList();

            // Add some default users if none exist
            if (!viewModel.AvailableUsers.Any())
            {
                viewModel.AvailableUsers = Enumerable.Range(1, 10)
                    .Select(i => new ProgressUserOption
                    {
                        Id = i,
                        Name = $"User {i}",
                        Email = $"user{i}@example.com",
                        EnrollmentCount = 0,
                        CompletedLessons = 0
                    })
                    .ToList();
            }
        }

        // GET: Admin/LessonProgresses/Statistics
        public async Task<IActionResult> Statistics()
        {
            var now = DateTime.Now;
            var thisMonth = new DateTime(now.Year, now.Month, 1);
            var thisYear = new DateTime(now.Year, 1, 1);

            var stats = new LessonProgressStatisticsViewModel
            {
                TotalProgresses = await _context.LessonProgresses.CountAsync(),
                CompletedProgresses = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage >= 100),
                InProgressProgresses = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage > 0 && lp.ProgressPercentage < 100),
                NotStartedProgresses = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage == 0),
                TotalLearners = await _context.LessonProgresses.Select(lp => lp.UserId).Distinct().CountAsync(),
                ActiveLearners = await _context.LessonProgresses
                    .Where(lp => lp.UpdatedAt >= thisMonth || (lp.UpdatedAt == null && lp.CreatedAt >= thisMonth))
                    .Select(lp => lp.UserId)
                    .Distinct()
                    .CountAsync()
            };

            // Calculate averages
            if (stats.TotalProgresses > 0)
            {
                stats.AverageProgress = await _context.LessonProgresses.AverageAsync(lp => lp.ProgressPercentage);
                stats.AverageTimeSpent = await _context.LessonProgresses
                    .Where(lp => lp.TimeSpent.HasValue)
                    .AverageAsync(lp => lp.TimeSpent.Value);
            }

            // Get trend data for last 30 days
            var trendData = new List<ProgressTrendData>();
            for (int i = 29; i >= 0; i--)
            {
                var date = DateTime.Now.Date.AddDays(-i);
                var nextDate = date.AddDays(1);

                var completedCount = await _context.LessonProgresses
                    .CountAsync(lp => lp.ProgressPercentage >= 100 &&
                               ((lp.UpdatedAt.HasValue && lp.UpdatedAt.Value.Date == date) ||
                                (!lp.UpdatedAt.HasValue && lp.CreatedAt.Date == date)));

                var startedCount = await _context.LessonProgresses
                    .CountAsync(lp => lp.CreatedAt.Date == date);

                var avgProgress = await _context.LessonProgresses
                    .Where(lp => lp.CreatedAt.Date <= date)
                    .AverageAsync(lp => (float?)lp.ProgressPercentage) ?? 0;

                trendData.Add(new ProgressTrendData
                {
                    Date = date,
                    CompletedCount = completedCount,
                    StartedCount = startedCount,
                    AverageProgress = avgProgress
                });
            }
            stats.TrendData = trendData;

            // Get top learners
            var topLearners = await _context.LessonProgresses
                .GroupBy(lp => lp.UserId)
                .Select(g => new TopLearnerData
                {
                    UserId = g.Key,
                    UserName = "User " + g.Key,
                    UserEmail = g.Key + "@example.com",
                    CompletedLessons = g.Count(lp => lp.ProgressPercentage >= 100),
                    TotalLessons = g.Count(),
                    AverageProgress = g.Average(lp => lp.ProgressPercentage),
                    TotalTimeSpent = g.Sum(lp => lp.TimeSpent ?? 0)
                })
                .OrderByDescending(x => x.CompletedLessons)
                .Take(10)
                .ToListAsync();

            stats.TopLearners = topLearners;

            // Get top lessons by progress (simplified to avoid EF translation issues)
            var lessonGroups = await _context.LessonProgresses
                .GroupBy(lp => lp.LessonId)
                .Select(g => new
                {
                    LessonId = g.Key,
                    TotalLearners = g.Count(),
                    CompletedLearners = g.Count(lp => lp.ProgressPercentage >= 100),
                    AverageProgress = g.Average(lp => lp.ProgressPercentage),
                    AverageTimeSpent = g.Average(lp => lp.TimeSpent ?? 0)
                })
                .ToListAsync();

            var topLessons = lessonGroups
                .Select(g => new TopLessonProgressData
                {
                    LessonId = g.LessonId,
                    LessonTitle = "Lesson " + g.LessonId,
                    CourseTitle = "Course Title",
                    ChapterName = "Chapter Name",
                    TotalLearners = g.TotalLearners,
                    CompletedLearners = g.CompletedLearners,
                    AverageProgress = g.AverageProgress,
                    AverageTimeSpent = g.AverageTimeSpent
                })
                .OrderByDescending(x => x.CompletionRate)
                .Take(10)
                .ToList();

            stats.TopLessons = topLessons;

            // Get recent progresses
            var recentProgresses = await _context.LessonProgresses
                .OrderByDescending(lp => lp.UpdatedAt ?? lp.CreatedAt)
                .Take(10)
                .Select(lp => new RecentProgressData
                {
                    Id = lp.Id,
                    LessonTitle = "Lesson " + lp.LessonId,
                    UserName = "User " + lp.UserId,
                    ProgressPercentage = lp.ProgressPercentage,
                    UpdatedAt = lp.UpdatedAt ?? lp.CreatedAt,
                    Status = lp.Status ?? "In Progress"
                })
                .ToListAsync();

            stats.RecentProgresses = recentProgresses;

            // Get progress distribution
            var progressDistribution = new List<ProgressDistributionData>
            {
                new ProgressDistributionData { Range = "0%", Count = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage == 0) },
                new ProgressDistributionData { Range = "1-25%", Count = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage > 0 && lp.ProgressPercentage <= 25) },
                new ProgressDistributionData { Range = "26-50%", Count = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage > 25 && lp.ProgressPercentage <= 50) },
                new ProgressDistributionData { Range = "51-75%", Count = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage > 50 && lp.ProgressPercentage <= 75) },
                new ProgressDistributionData { Range = "76-99%", Count = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage > 75 && lp.ProgressPercentage < 100) },
                new ProgressDistributionData { Range = "100%", Count = await _context.LessonProgresses.CountAsync(lp => lp.ProgressPercentage >= 100) }
            };

            foreach (var item in progressDistribution)
            {
                item.Percentage = stats.TotalProgresses > 0 ? (float)item.Count / stats.TotalProgresses * 100 : 0;
            }

            stats.ProgressDistribution = progressDistribution;

            // Get course progress summaries (simplified)
            var courseProgressSummaries = new List<CourseProgressSummary>
            {
                new CourseProgressSummary { CourseId = 1, CourseTitle = "Course 1", TotalLessons = 10, TotalLearners = 25, CompletedLearners = 15, AverageProgress = 75.5f },
                new CourseProgressSummary { CourseId = 2, CourseTitle = "Course 2", TotalLessons = 8, TotalLearners = 20, CompletedLearners = 12, AverageProgress = 68.2f },
                new CourseProgressSummary { CourseId = 3, CourseTitle = "Course 3", TotalLessons = 12, TotalLearners = 30, CompletedLearners = 18, AverageProgress = 82.1f }
            };

            stats.CourseProgressSummaries = courseProgressSummaries;

            return View(stats);
        }

        // POST: Admin/LessonProgresses/UpdateProgress/5
        [HttpPost]
        public async Task<IActionResult> UpdateProgress(int id, [FromBody] UpdateProgressRequest request)
        {
            try
            {
                var progress = await _context.LessonProgresses.FindAsync(id);
                if (progress == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy tiến độ học tập" });
                }

                if (request.ProgressPercentage < 0 || request.ProgressPercentage > 100)
                {
                    return Json(new { success = false, message = "Tiến độ phải từ 0 đến 100%" });
                }

                progress.ProgressPercentage = request.ProgressPercentage;
                progress.UpdatedAt = DateTime.Now;

                // Update status based on progress
                if (request.ProgressPercentage >= 100)
                {
                    progress.Status = "Completed";
                }
                else if (request.ProgressPercentage > 0)
                {
                    progress.Status = "In Progress";
                }
                else
                {
                    progress.Status = "Not Started";
                }

                await _context.SaveChangesAsync();

                return Json(new {
                    success = true,
                    message = "Cập nhật tiến độ thành công",
                    newProgress = request.ProgressPercentage,
                    newStatus = progress.Status
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        public class UpdateProgressRequest
        {
            public float ProgressPercentage { get; set; }
        }

        // POST: Admin/LessonProgresses/BulkUpdateStatus
        [HttpPost]
        public async Task<IActionResult> BulkUpdateStatus([FromBody] BulkUpdateStatusRequest request)
        {
            try
            {
                if (request.Ids == null || !request.Ids.Any())
                {
                    return Json(new { success = false, message = "Vui lòng chọn ít nhất một tiến độ" });
                }

                var progresses = await _context.LessonProgresses
                    .Where(lp => request.Ids.Contains(lp.Id))
                    .ToListAsync();

                foreach (var progress in progresses)
                {
                    progress.Status = request.Status;
                    progress.UpdatedAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                return Json(new {
                    success = true,
                    message = $"Cập nhật trạng thái cho {progresses.Count} tiến độ thành công"
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        public class BulkUpdateStatusRequest
        {
            public List<int> Ids { get; set; } = new List<int>();
            public string Status { get; set; } = "";
        }
    }
}
