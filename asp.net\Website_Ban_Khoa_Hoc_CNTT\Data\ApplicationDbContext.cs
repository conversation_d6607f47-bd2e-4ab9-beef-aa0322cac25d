using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ELearningWebsite.Models;

namespace ELearningWebsite.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets cho các bảng có sẵn trong database
        public DbSet<Category> Categories { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<Chapter> Chapters { get; set; }
        public DbSet<Answer> Answers { get; set; }
        public DbSet<Enrollment> Enrollments { get; set; }
        public DbSet<LessonProgress> LessonProgresses { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<Certificate> Certificates { get; set; }
        public DbSet<Discount> Discounts { get; set; }
        public DbSet<Finance> Finances { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure relationships cho các bảng có sẵn
            builder.Entity<Course>()
                .HasOne(c => c.Category)
                .WithMany(cat => cat.Courses)
                .HasForeignKey(c => c.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Duration as int (minutes) instead of TimeSpan
            builder.Entity<Course>()
                .Property(c => c.Duration)
                .HasColumnType("int");

            builder.Entity<Chapter>()
                .HasOne(ch => ch.Course)
                .WithMany(c => c.Chapters)
                .HasForeignKey(ch => ch.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Enrollment>()
                .HasOne(e => e.Course)
                .WithMany(c => c.Enrollments)
                .HasForeignKey(e => e.CourseId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Certificate>()
                .HasOne(cert => cert.Enrollment)
                .WithMany(e => e.Certificates)
                .HasForeignKey(cert => cert.EnrollmentId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Discount>()
                .HasOne(d => d.Course)
                .WithMany(c => c.Discounts)
                .HasForeignKey(d => d.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Comment>()
                .HasOne(c => c.ParentComment)
                .WithMany(pc => pc.Replies)
                .HasForeignKey(c => c.ParentCommentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure indexes
            builder.Entity<Course>()
                .HasIndex(c => c.Status);

            builder.Entity<Discount>()
                .HasIndex(d => d.Code)
                .IsUnique();

            // Configure ApplicationUser relationships
            // Không cấu hình relationship với Enrollment và LessonProgress vì UserId là int, không phải string

            // Không seed data vì sử dụng database có sẵn
        }

    }
}
