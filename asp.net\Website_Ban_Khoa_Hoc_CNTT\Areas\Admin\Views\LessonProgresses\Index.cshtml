@model ELearningWebsite.Areas.Admin.ViewModels.LessonProgressIndexViewModel
@{
    ViewData["Title"] = "Quản lý Tiến độ Học tập";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-chart-line text-primary"></i>
                Quản lý Tiến độ Học tập
            </h1>
            <p class="admin-page-subtitle">Theo dõi và quản lý tiến độ học tập của học viên</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus"></i> Tạo tiến độ mới
            </a>
            <a asp-action="Statistics" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> Thống kê
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="admin-filters-card">
    <form method="get" class="admin-filters-form">
        <div class="row g-3">
            <div class="col-md-2">
                <label class="form-label">Tìm kiếm</label>
                <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" 
                       placeholder="User ID, trạng thái...">
            </div>
            <div class="col-md-2">
                <label class="form-label">Bài học</label>
                <select name="lessonId" class="form-select">
                    <option value="">Tất cả bài học</option>
                    @foreach (var lesson in Model.AvailableLessons)
                    {
                        <option value="@lesson.Id" selected="@(Model.LessonId == lesson.Id)">
                            @lesson.Title
                        </option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Học viên</label>
                <select name="userId" class="form-select">
                    <option value="">Tất cả học viên</option>
                    @foreach (var user in Model.AvailableUsers)
                    {
                        <option value="@user.Id" selected="@(Model.UserId == user.Id)">
                            @user.Name
                        </option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="completed" selected="@(Model.Status == "completed")">Hoàn thành</option>
                    <option value="in-progress" selected="@(Model.Status == "in-progress")">Đang học</option>
                    <option value="not-started" selected="@(Model.Status == "not-started")">Chưa bắt đầu</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Tiến độ (%)</label>
                <div class="d-flex gap-1">
                    <input type="number" name="minProgress" value="@Model.MinProgress" class="form-control" 
                           placeholder="Min" min="0" max="100">
                    <input type="number" name="maxProgress" value="@Model.MaxProgress" class="form-control" 
                           placeholder="Max" min="0" max="100">
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm
                    </button>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Results Summary -->
<div class="admin-summary-cards">
    <div class="row">
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-primary">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.TotalItems</h3>
                    <p>Tổng tiến độ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.LessonProgresses.Count(lp => lp.IsCompleted)</h3>
                    <p>Hoàn thành</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.LessonProgresses.Count(lp => lp.ProgressPercentage > 0 && !lp.IsCompleted)</h3>
                    <p>Đang học</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-summary-card">
                <div class="summary-icon bg-secondary">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <div class="summary-content">
                    <h3>@Model.LessonProgresses.Count(lp => lp.ProgressPercentage == 0)</h3>
                    <p>Chưa bắt đầu</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Table -->
<div class="admin-table-card">
    <div class="table-responsive">
        <table class="table admin-table">
            <thead>
                <tr>
                    <th>
                        <input type="checkbox" id="selectAll" class="form-check-input">
                    </th>
                    <th>Bài học</th>
                    <th>Học viên</th>
                    <th>Tiến độ</th>
                    <th>Thời gian học</th>
                    <th>Điểm cao nhất</th>
                    <th>Trạng thái</th>
                    <th>Cập nhật</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.LessonProgresses.Any())
                {
                    @foreach (var progress in Model.LessonProgresses)
                    {
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input progress-checkbox" value="@progress.Id">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle text-primary me-2"></i>
                                    <div>
                                        <strong>@progress.LessonTitle</strong>
                                        <br>
                                        <small class="text-muted">@progress.CourseTitle</small>
                                        @if (!string.IsNullOrEmpty(progress.ChapterName))
                                        {
                                            <br>
                                            <small class="text-info">@progress.ChapterName</small>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>@progress.UserName</strong>
                                    <br>
                                    <small class="text-muted">@progress.UserEmail</small>
                                    <br>
                                    <small class="text-info">ID: @progress.UserId</small>
                                </div>
                            </td>
                            <td>
                                <div class="progress-container">
                                    <div class="progress mb-1" style="height: 8px;">
                                        <div class="progress-bar <EMAIL>" 
                                             style="width: @progress.ProgressPercentage%"></div>
                                    </div>
                                    <small class="<EMAIL>">
                                        @progress.ProgressPercentage.ToString("F1")%
                                    </small>
                                    <button class="btn btn-sm btn-outline-primary ms-1" 
                                            onclick="editProgress(@progress.Id, @progress.ProgressPercentage)" 
                                            title="Chỉnh sửa tiến độ">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                @if (progress.TimeSpent.HasValue)
                                {
                                    <span class="text-info">@progress.TimeSpent.Value.ToString("F1") phút</span>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa có</span>
                                }
                            </td>
                            <td>
                                @if (progress.HighestMark.HasValue)
                                {
                                    <span class="badge bg-@(progress.HighestMark >= 80 ? "success" : progress.HighestMark >= 60 ? "warning" : "danger")">
                                        @progress.HighestMark.Value.ToString("F1")
                                    </span>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa có</span>
                                }
                            </td>
                            <td>
                                <span class="badge <EMAIL>">
                                    @progress.ProgressStatus
                                </span>
                                @if (progress.IsPassing)
                                {
                                    <br>
                                    <small class="text-success">
                                        <i class="fas fa-check"></i> Đạt
                                    </small>
                                }
                            </td>
                            <td>
                                @if (progress.UpdatedAt.HasValue)
                                {
                                    <span class="text-success">@progress.UpdatedAt.Value.ToString("dd/MM/yyyy")</span>
                                    <br>
                                    <small class="text-muted">@progress.UpdatedAt.Value.ToString("HH:mm")</small>
                                }
                                else
                                {
                                    <span class="text-muted">@progress.CreatedAt.ToString("dd/MM/yyyy")</span>
                                    <br>
                                    <small class="text-muted">@progress.CreatedAt.ToString("HH:mm")</small>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@progress.Id" 
                                       class="btn btn-sm btn-outline-info" title="Chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@progress.Id" 
                                       class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@progress.Id" 
                                       class="btn btn-sm btn-outline-danger" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <div class="empty-state">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Không có tiến độ học tập nào</h5>
                                <p class="text-muted">Chưa có tiến độ học tập nào được tạo hoặc không khớp với bộ lọc</p>
                                <a asp-action="Create" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Tạo tiến độ đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<!-- Bulk Actions -->
@if (Model.LessonProgresses.Any())
{
    <div class="bulk-actions-card" id="bulkActions" style="display: none;">
        <div class="d-flex align-items-center gap-3">
            <span class="selected-count">Đã chọn: <strong id="selectedCount">0</strong> tiến độ</span>
            <div class="bulk-action-buttons">
                <button class="btn btn-outline-success" onclick="bulkUpdateStatus('Completed')">
                    <i class="fas fa-check"></i> Đánh dấu hoàn thành
                </button>
                <button class="btn btn-outline-warning" onclick="bulkUpdateStatus('In Progress')">
                    <i class="fas fa-clock"></i> Đánh dấu đang học
                </button>
                <button class="btn btn-outline-secondary" onclick="bulkUpdateStatus('Not Started')">
                    <i class="fas fa-pause"></i> Đánh dấu chưa bắt đầu
                </button>
            </div>
        </div>
    </div>
}

<!-- Pagination -->
@if (Model.TotalPages > 1)
{
    <nav aria-label="Progress pagination">
        <ul class="pagination justify-content-center">
            @if (Model.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" asp-action="Index" 
                       asp-route-page="@(Model.CurrentPage - 1)"
                       asp-route-searchTerm="@Model.SearchTerm"
                       asp-route-lessonId="@Model.LessonId"
                       asp-route-userId="@Model.UserId"
                       asp-route-status="@Model.Status"
                       asp-route-minProgress="@Model.MinProgress"
                       asp-route-maxProgress="@Model.MaxProgress">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                    <a class="page-link" asp-action="Index" 
                       asp-route-page="@i"
                       asp-route-searchTerm="@Model.SearchTerm"
                       asp-route-lessonId="@Model.LessonId"
                       asp-route-userId="@Model.UserId"
                       asp-route-status="@Model.Status"
                       asp-route-minProgress="@Model.MinProgress"
                       asp-route-maxProgress="@Model.MaxProgress">
                        @i
                    </a>
                </li>
            }

            @if (Model.CurrentPage < Model.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" asp-action="Index" 
                       asp-route-page="@(Model.CurrentPage + 1)"
                       asp-route-searchTerm="@Model.SearchTerm"
                       asp-route-lessonId="@Model.LessonId"
                       asp-route-userId="@Model.UserId"
                       asp-route-status="@Model.Status"
                       asp-route-minProgress="@Model.MinProgress"
                       asp-route-maxProgress="@Model.MaxProgress">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<!-- Edit Progress Modal -->
<div class="modal fade" id="editProgressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa tiến độ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProgressForm">
                    <input type="hidden" id="editProgressId">
                    <div class="mb-3">
                        <label class="form-label">Tiến độ (%)</label>
                        <input type="number" id="editProgressValue" class="form-control" min="0" max="100" step="0.1">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveProgress()">Lưu</button>
            </div>
        </div>
    </div>
</div>

<script>
// Checkbox handling
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.progress-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
    updateBulkActions();
});

document.querySelectorAll('.progress-checkbox').forEach(cb => {
    cb.addEventListener('change', updateBulkActions);
});

function updateBulkActions() {
    const selected = document.querySelectorAll('.progress-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selected.length > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = selected.length;
    } else {
        bulkActions.style.display = 'none';
    }
}

// Edit progress
function editProgress(id, currentProgress) {
    document.getElementById('editProgressId').value = id;
    document.getElementById('editProgressValue').value = currentProgress;
    new bootstrap.Modal(document.getElementById('editProgressModal')).show();
}

function saveProgress() {
    const id = document.getElementById('editProgressId').value;
    const progress = document.getElementById('editProgressValue').value;
    
    fetch(`/Admin/LessonProgresses/UpdateProgress/${id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
        },
        body: JSON.stringify({ progressPercentage: parseFloat(progress) })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editProgressModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            toastr.error(data.message);
        }
    })
    .catch(error => {
        toastr.error('Có lỗi xảy ra khi cập nhật tiến độ');
    });
}

// Bulk update status
function bulkUpdateStatus(status) {
    const selected = Array.from(document.querySelectorAll('.progress-checkbox:checked')).map(cb => parseInt(cb.value));
    
    if (selected.length === 0) {
        toastr.warning('Vui lòng chọn ít nhất một tiến độ');
        return;
    }
    
    if (confirm(`Bạn có chắc chắn muốn cập nhật trạng thái cho ${selected.length} tiến độ?`)) {
        fetch('/Admin/LessonProgresses/BulkUpdateStatus', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            },
            body: JSON.stringify({ ids: selected, status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                toastr.error(data.message);
            }
        })
        .catch(error => {
            toastr.error('Có lỗi xảy ra khi cập nhật trạng thái');
        });
    }
}
</script>

<style>
.progress-container {
    min-width: 120px;
}

.bulk-actions-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.selected-count {
    font-weight: 500;
}

.bulk-action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}
</style>
