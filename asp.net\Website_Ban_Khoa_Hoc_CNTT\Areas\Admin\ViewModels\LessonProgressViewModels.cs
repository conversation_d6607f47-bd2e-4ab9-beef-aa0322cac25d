using System.ComponentModel.DataAnnotations;
using ELearningWebsite.Models;

namespace ELearningWebsite.Areas.Admin.ViewModels
{
    public class LessonProgressIndexViewModel
    {
        public List<LessonProgressListItem> LessonProgresses { get; set; } = new();
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalItems { get; set; }
        public int PageSize { get; set; } = 10;
        public string SearchTerm { get; set; } = string.Empty;
        public int? LessonId { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // all, completed, in-progress, not-started
        public float? MinProgress { get; set; }
        public float? MaxProgress { get; set; }
        public List<ProgressLessonOption> AvailableLessons { get; set; } = new();
        public List<ProgressUserOption> AvailableUsers { get; set; } = new();
    }

    public class LessonProgressListItem
    {
        public int Id { get; set; }
        public int LessonId { get; set; }
        public string LessonTitle { get; set; } = string.Empty;
        public string CourseTitle { get; set; } = string.Empty;
        public string ChapterName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public float ProgressPercentage { get; set; }
        public float? TimeSpent { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? Status { get; set; }
        public int? Passing { get; set; }
        public int? CountDoing { get; set; }
        public float? HighestMark { get; set; }
        public string ProgressStatus => GetProgressStatus();
        public string ProgressColor => GetProgressColor();
        public bool IsCompleted => ProgressPercentage >= 100;
        public bool IsPassing => Passing == 1;

        private string GetProgressStatus()
        {
            if (ProgressPercentage >= 100) return "Hoàn thành";
            if (ProgressPercentage > 0) return "Đang học";
            return "Chưa bắt đầu";
        }

        private string GetProgressColor()
        {
            if (ProgressPercentage >= 100) return "success";
            if (ProgressPercentage >= 50) return "warning";
            if (ProgressPercentage > 0) return "info";
            return "secondary";
        }
    }

    public class LessonProgressDetailsViewModel
    {
        public int Id { get; set; }
        public int LessonId { get; set; }
        public string LessonTitle { get; set; } = string.Empty;
        public string LessonDescription { get; set; } = string.Empty;
        public string LessonType { get; set; } = string.Empty;
        public string CourseTitle { get; set; } = string.Empty;
        public string ChapterName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public float ProgressPercentage { get; set; }
        public float? TimeSpent { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? Status { get; set; }
        public int? Passing { get; set; }
        public int? CountDoing { get; set; }
        public float? HighestMark { get; set; }
        public string ProgressStatus => GetProgressStatus();
        public bool IsCompleted => ProgressPercentage >= 100;
        public bool IsPassing => Passing == 1;
        public List<ProgressHistoryItem> ProgressHistory { get; set; } = new();

        private string GetProgressStatus()
        {
            if (ProgressPercentage >= 100) return "Hoàn thành";
            if (ProgressPercentage > 0) return "Đang học";
            return "Chưa bắt đầu";
        }
    }

    public class LessonProgressCreateViewModel
    {
        [Required(ErrorMessage = "Vui lòng chọn bài học")]
        public int LessonId { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn học viên")]
        public string UserId { get; set; } = string.Empty;

        [Range(0, 100, ErrorMessage = "Tiến độ phải từ 0 đến 100%")]
        public float ProgressPercentage { get; set; } = 0;

        [Range(0, float.MaxValue, ErrorMessage = "Thời gian học phải >= 0")]
        public float? TimeSpent { get; set; }

        public string? Status { get; set; } = "In Progress";

        public int? Passing { get; set; } = 1;

        public int? CountDoing { get; set; } = 1;

        [Range(0, 100, ErrorMessage = "Điểm cao nhất phải từ 0 đến 100")]
        public float? HighestMark { get; set; }

        public List<ProgressLessonOption> AvailableLessons { get; set; } = new();
        public List<ProgressUserOption> AvailableUsers { get; set; } = new();

        // For display purposes
        public string LessonTitle { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
    }

    public class LessonProgressEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn bài học")]
        public int LessonId { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn học viên")]
        public string UserId { get; set; } = string.Empty;

        [Range(0, 100, ErrorMessage = "Tiến độ phải từ 0 đến 100%")]
        public float ProgressPercentage { get; set; }

        [Range(0, float.MaxValue, ErrorMessage = "Thời gian học phải >= 0")]
        public float? TimeSpent { get; set; }

        public string? Status { get; set; }

        public int? Passing { get; set; }

        public int? CountDoing { get; set; }

        [Range(0, 100, ErrorMessage = "Điểm cao nhất phải từ 0 đến 100")]
        public float? HighestMark { get; set; }

        public List<ProgressLessonOption> AvailableLessons { get; set; } = new();
        public List<ProgressUserOption> AvailableUsers { get; set; } = new();

        // For display purposes
        public string LessonTitle { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class LessonProgressDeleteViewModel
    {
        public int Id { get; set; }
        public string LessonTitle { get; set; } = string.Empty;
        public string CourseTitle { get; set; } = string.Empty;
        public string ChapterName { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public float ProgressPercentage { get; set; }
        public float? TimeSpent { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? Status { get; set; }
        public int? CountDoing { get; set; }
        public float? HighestMark { get; set; }
        public bool IsCompleted => ProgressPercentage >= 100;
    }

    public class LessonProgressStatisticsViewModel
    {
        public int TotalProgresses { get; set; }
        public int CompletedProgresses { get; set; }
        public int InProgressProgresses { get; set; }
        public int NotStartedProgresses { get; set; }
        public float AverageProgress { get; set; }
        public float AverageTimeSpent { get; set; }
        public int TotalLearners { get; set; }
        public int ActiveLearners { get; set; }
        public List<ProgressTrendData> TrendData { get; set; } = new();
        public List<TopLearnerData> TopLearners { get; set; } = new();
        public List<TopLessonProgressData> TopLessons { get; set; } = new();
        public List<RecentProgressData> RecentProgresses { get; set; } = new();
        public List<ProgressDistributionData> ProgressDistribution { get; set; } = new();
        public List<CourseProgressSummary> CourseProgressSummaries { get; set; } = new();
    }

    public class ProgressTrendData
    {
        public DateTime Date { get; set; }
        public int CompletedCount { get; set; }
        public int StartedCount { get; set; }
        public float AverageProgress { get; set; }
    }

    public class TopLearnerData
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public int CompletedLessons { get; set; }
        public int TotalLessons { get; set; }
        public float AverageProgress { get; set; }
        public float TotalTimeSpent { get; set; }
        public float CompletionRate => TotalLessons > 0 ? (float)CompletedLessons / TotalLessons * 100 : 0;
    }

    public class TopLessonProgressData
    {
        public int LessonId { get; set; }
        public string LessonTitle { get; set; } = string.Empty;
        public string CourseTitle { get; set; } = string.Empty;
        public string ChapterName { get; set; } = string.Empty;
        public int TotalLearners { get; set; }
        public int CompletedLearners { get; set; }
        public float AverageProgress { get; set; }
        public float AverageTimeSpent { get; set; }
        public float CompletionRate => TotalLearners > 0 ? (float)CompletedLearners / TotalLearners * 100 : 0;
    }

    public class RecentProgressData
    {
        public int Id { get; set; }
        public string LessonTitle { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public float ProgressPercentage { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool IsCompleted => ProgressPercentage >= 100;
    }

    public class ProgressDistributionData
    {
        public string Range { get; set; } = string.Empty; // 0-25%, 26-50%, etc.
        public int Count { get; set; }
        public float Percentage { get; set; }
    }

    public class CourseProgressSummary
    {
        public int CourseId { get; set; }
        public string CourseTitle { get; set; } = string.Empty;
        public int TotalLessons { get; set; }
        public int TotalLearners { get; set; }
        public int CompletedLearners { get; set; }
        public float AverageProgress { get; set; }
        public float CompletionRate => TotalLearners > 0 ? (float)CompletedLearners / TotalLearners * 100 : 0;
    }

    public class ProgressHistoryItem
    {
        public DateTime Date { get; set; }
        public float ProgressPercentage { get; set; }
        public float? TimeSpent { get; set; }
        public string Action { get; set; } = string.Empty; // Started, Updated, Completed
        public string? Notes { get; set; }
    }

    // Helper classes
    public class ProgressLessonOption
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string CourseTitle { get; set; } = string.Empty;
        public string ChapterName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int LearnerCount { get; set; }
    }

    public class ProgressUserOption
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public int EnrollmentCount { get; set; }
        public int CompletedLessons { get; set; }
    }
}
