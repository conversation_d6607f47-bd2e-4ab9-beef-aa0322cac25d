@{
    ViewData["Title"] = "Thống kê Tài chính";
}

<!-- Admin Finance Statistics -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="admin-title">
            <i class="fas fa-chart-bar me-2"></i>
            Thống kê Tài chính
        </h2>
        <a asp-action="Index" class="admin-btn admin-btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Quay lại
        </a>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-success">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">T<PERSON>ng doanh thu</div>
                            <div class="h4 mb-0 font-weight-bold">@((double)(ViewBag.TotalRevenue ?? 0)).ToString("N0") VNĐ</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-warning">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng phí</div>
                            <div class="h4 mb-0 font-weight-bold">@((double)(ViewBag.TotalFee ?? 0)).ToString("N0") VNĐ</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-minus-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-primary">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Doanh thu ròng</div>
                            <div class="h4 mb-0 font-weight-bold">@((double)(ViewBag.NetRevenue ?? 0)).ToString("N0") VNĐ</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card stat-card-info">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 opacity-75">Tổng bản ghi</div>
                            <div class="h4 mb-0 font-weight-bold">@(ViewBag.TotalRecords ?? 0)</div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Thống kê tài chính</h5>
                </div>
                <div class="admin-card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-4x text-primary mb-4"></i>
                        <h4 class="text-primary">Trang thống kê tài chính</h4>
                        <p class="text-muted mb-4">
                            Trang này hiển thị các thống kê tổng quan về tài chính của hệ thống.
                            Bao gồm doanh thu, phí, và các chỉ số quan trọng khác.
                        </p>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Thông tin hiển thị:</h6>
                                    <ul class="text-start mb-0">
                                        <li>Tổng doanh thu từ tất cả các giao dịch</li>
                                        <li>Tổng phí đã thu</li>
                                        <li>Doanh thu ròng (doanh thu - phí)</li>
                                        <li>Tổng số bản ghi tài chính</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <a asp-action="Index" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            Quay lại danh sách
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


