@model IEnumerable<Enrollment>
@{
    ViewData["Title"] = "Quản lý Đăng ký";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quản lý Đăng ký khóa học</h3>
                    <div class="card-tools">
                        <a asp-action="Statistics" class="btn btn-info btn-sm">
                            <i class="fas fa-chart-bar"></i> Thống kê
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- Search and Filter -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="search" value="@ViewBag.Search" class="form-control" placeholder="Tìm kiếm theo tên khóa học, học viên...">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">-- Tất cả trạng thái --</option>
                                    <option value="1" selected="@(ViewBag.Status == "1")">Đang học</option>
                                    <option value="3" selected="@(ViewBag.Status == "3")">Hoàn thành</option>
                                    <option value="2" selected="@(ViewBag.Status == "2")">Tạm dừng</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Tìm kiếm
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                    <i class="fas fa-refresh"></i> Làm mới
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Enrollments Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Học viên</th>
                                    <th>Khóa học</th>
                                    <th>Ngày đăng ký</th>
                                    <th>Ngày hết hạn</th>
                                    <th>Tiến độ</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model != null && Model.Any())
                                {
                                    @foreach (var enrollment in Model)
                                    {
                                        <tr>
                                            <td>@enrollment.Id</td>
                                            <td>
                                                <div>
                                                    <strong>User ID: @enrollment.UserId</strong><br>
                                                    <small class="text-muted">Cần load thông tin user</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>@enrollment.Course?.Title</strong><br>
                                                    <small class="text-muted">@enrollment.Course?.Price.ToString("N0") VNĐ</small>
                                                </div>
                                            </td>
                                            <td>@enrollment.EnrollmentDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @if (enrollment.ExpiredDate.HasValue)
                                                {
                                                    @enrollment.ExpiredDate.Value.ToString("dd/MM/yyyy")
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Không giới hạn</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" style="width: @enrollment.Progress%"
                                                         aria-valuenow="@enrollment.Progress" aria-valuemin="0" aria-valuemax="100">
                                                        @enrollment.Progress%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @switch (enrollment.Status)
                                                {
                                                    case 1:
                                                        <span class="badge bg-success">Đang học</span>
                                                        break;
                                                    case 3:
                                                        <span class="badge bg-primary">Hoàn thành</span>
                                                        break;
                                                    case 2:
                                                        <span class="badge bg-danger">Tạm dừng</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@enrollment.GetStatusText()</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@enrollment.Id" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>

                                                    <!-- Status Update Dropdown -->
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-warning btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="#" onclick="updateStatus(@enrollment.Id, 1)">Đang học</a></li>
                                                            <li><a class="dropdown-item" href="#" onclick="updateStatus(@enrollment.Id, 3)">Hoàn thành</a></li>
                                                            <li><a class="dropdown-item" href="#" onclick="updateStatus(@enrollment.Id, 2)">Tạm dừng</a></li>
                                                        </ul>
                                                    </div>

                                                    <form asp-action="Delete" asp-route-id="@enrollment.Id" method="post" style="display: inline;"
                                                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa đăng ký này?')">
                                                        @Html.AntiForgeryToken()
                                                        <button type="submit" class="btn btn-danger btn-sm">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="8" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (ViewBag.TotalPages > 1)
                    {
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                @if (ViewBag.CurrentPage > 1)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Index", new { page = ViewBag.CurrentPage - 1, search = ViewBag.Search, status = ViewBag.Status })">Trước</a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                                {
                                    <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="@Url.Action("Index", new { page = i, search = ViewBag.Search, status = ViewBag.Status })">@i</a>
                                    </li>
                                }

                                @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Index", new { page = ViewBag.CurrentPage + 1, search = ViewBag.Search, status = ViewBag.Status })">Sau</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }

                    <div class="mt-3">
                        <small class="text-muted">
                            Hiển thị @(((int?)ViewBag.CurrentPage ?? 1 - 1) * ((int?)ViewBag.PageSize ?? 10) + 1) - @(Math.Min(((int?)ViewBag.CurrentPage ?? 1) * ((int?)ViewBag.PageSize ?? 10), ((int?)ViewBag.TotalItems ?? 0)))
                            trong tổng số @(ViewBag.TotalItems ?? 0) đăng ký
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function updateStatus(enrollmentId, status) {
        if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái?')) {
            const formData = new FormData();
            formData.append('id', enrollmentId);
            formData.append('status', status);
            formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

            fetch('@Url.Action("UpdateStatus")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Có lỗi xảy ra: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi cập nhật trạng thái');
            });
        }
    }
</script>

@Html.AntiForgeryToken()
