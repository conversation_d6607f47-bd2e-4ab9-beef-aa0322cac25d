using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ELearningWebsite.Data;
using ELearningWebsite.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace ELearningWebsite.Controllers
{
    [Authorize]
    public class CommentController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<CommentController> _logger;

        public CommentController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<CommentController> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: Comment/GetComments/5
        [HttpGet]
        public async Task<IActionResult> GetComments(int courseId)
        {
            try
            {
                // Since we only have LessonId in Comment model, we'll use courseId as lessonId for now
                // This is a temporary solution until we have proper lesson structure
                var comments = await _context.Comments
                    .Where(c => c.LessonId == courseId && !c.IsDelete && c.ParentCommentId == null)
                    .Include(c => c.Replies.Where(r => !r.IsDelete))
                    .OrderByDescending(c => c.CreatedAt)
                    .Select(c => new CommentViewModel
                    {
                        Id = c.Id,
                        Content = c.Content ?? "",
                        CreatedAt = c.CreatedAt,
                        UserId = c.UserId,
                        UserName = GetUserName(c.UserId),
                        UserAvatar = "/images/default-avatar.png",
                        Replies = c.Replies.Select(r => new CommentViewModel
                        {
                            Id = r.Id,
                            Content = r.Content ?? "",
                            CreatedAt = r.CreatedAt,
                            UserId = r.UserId,
                            UserName = GetUserName(r.UserId),
                            UserAvatar = "/images/default-avatar.png",
                            ParentCommentId = r.ParentCommentId
                        }).OrderBy(r => r.CreatedAt).ToList()
                    })
                    .ToListAsync();

                return Json(new { success = true, comments = comments });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting comments for course {CourseId}", courseId);
                return Json(new { success = false, message = "Có lỗi xảy ra khi tải bình luận" });
            }
        }

        // POST: Comment/PostComment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PostComment([FromBody] PostCommentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = "Dữ liệu không hợp lệ" });
                }

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { success = false, message = "Vui lòng đăng nhập để bình luận" });
                }

                // Convert string UserId to int
                int currentUserId = GetUserIdAsInt(currentUser.Id);

                var comment = new Comment
                {
                    LessonId = request.CourseId, // Using courseId as lessonId temporarily
                    UserId = currentUserId,
                    Content = request.Content.Trim(),
                    ParentCommentId = request.ParentCommentId,
                    CreatedAt = DateTime.Now,
                    IsDelete = false
                };

                _context.Comments.Add(comment);
                await _context.SaveChangesAsync();

                var commentViewModel = new CommentViewModel
                {
                    Id = comment.Id,
                    Content = comment.Content,
                    CreatedAt = comment.CreatedAt,
                    UserId = comment.UserId,
                    UserName = currentUser.FullName ?? currentUser.UserName ?? "Anonymous",
                    UserAvatar = "/images/default-avatar.png",
                    ParentCommentId = comment.ParentCommentId,
                    Replies = new List<CommentViewModel>()
                };

                return Json(new { 
                    success = true, 
                    message = "Bình luận đã được đăng thành công",
                    comment = commentViewModel 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error posting comment");
                return Json(new { success = false, message = "Có lỗi xảy ra khi đăng bình luận" });
            }
        }

        // POST: Comment/DeleteComment/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteComment(int id)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { success = false, message = "Vui lòng đăng nhập" });
                }

                int currentUserId = GetUserIdAsInt(currentUser.Id);

                var comment = await _context.Comments.FindAsync(id);
                if (comment == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy bình luận" });
                }

                // Only allow user to delete their own comments
                if (comment.UserId != currentUserId)
                {
                    return Json(new { success = false, message = "Bạn chỉ có thể xóa bình luận của mình" });
                }

                comment.IsDelete = true;
                comment.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Bình luận đã được xóa" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting comment {CommentId}", id);
                return Json(new { success = false, message = "Có lỗi xảy ra khi xóa bình luận" });
            }
        }

        private string GetUserName(int? userId)
        {
            if (!userId.HasValue) return "Anonymous";
            
            // This is a simplified approach. In a real application, you might want to cache this
            // or use a more efficient method to get user names
            var user = _context.Users.FirstOrDefault(u => u.Id == userId.ToString());
            return user?.FullName ?? user?.UserName ?? $"User {userId}";
        }

        private int GetUserIdAsInt(string stringUserId)
        {
            if (int.TryParse(stringUserId, out int userId))
            {
                return userId;
            }

            // Fallback to 1023 since it exists in the database
            _logger.LogWarning("Could not parse UserId '{UserId}' to int, using fallback {FallbackId}", stringUserId, 1023);
            return 1023;
        }
    }

    // ViewModels
    public class CommentViewModel
    {
        public int Id { get; set; }
        public string Content { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public int? UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string UserAvatar { get; set; } = string.Empty;
        public int? ParentCommentId { get; set; }
        public List<CommentViewModel> Replies { get; set; } = new();
    }

    public class PostCommentRequest
    {
        [Required(ErrorMessage = "Nội dung bình luận là bắt buộc")]
        [StringLength(2000, ErrorMessage = "Nội dung không được vượt quá 2000 ký tự")]
        public string Content { get; set; } = string.Empty;

        [Required]
        public int CourseId { get; set; }

        public int? ParentCommentId { get; set; }
    }
}
