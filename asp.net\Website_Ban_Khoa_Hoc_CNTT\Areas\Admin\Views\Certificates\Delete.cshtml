@model ELearningWebsite.Areas.Admin.ViewModels.CertificateDeleteViewModel
@{
    ViewData["Title"] = "Xóa Chứng chỉ";
}

<div class="admin-content-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-trash text-danger"></i>
                Xóa Chứng chỉ
            </h1>
            <p class="admin-page-subtitle">Xác nhận xóa chứng chỉ #@Model.CertificateNumber</p>
        </div>
        <div class="admin-actions">
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                <i class="fas fa-eye"></i> Xem chi tiết
            </a>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="admin-form-card">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    Xác nhận xóa chứng chỉ
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <i class="fas fa-warning"></i> Cảnh báo!
                    </h6>
                    <p class="mb-0">
                        Bạn đang thực hiện xóa chứng chỉ. Hành động này <strong>không thể hoàn tác</strong>.
                        Vui lòng kiểm tra kỹ thông tin trước khi xác nhận.
                    </p>
                </div>

                <!-- Certificate Information -->
                <div class="certificate-info">
                    <h6 class="section-title">
                        <i class="fas fa-certificate text-warning"></i>
                        Thông tin chứng chỉ sẽ bị xóa:
                    </h6>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <label>ID Chứng chỉ:</label>
                            <span class="info-value">#@Model.Id</span>
                        </div>
                        <div class="info-item">
                            <label>Số chứng chỉ:</label>
                            <span class="info-value text-primary">@Model.CertificateNumber</span>
                        </div>
                        <div class="info-item">
                            <label>Học viên:</label>
                            <span class="info-value">@Model.UserName</span>
                        </div>
                        <div class="info-item">
                            <label>Khóa học:</label>
                            <span class="info-value">@Model.CourseTitle</span>
                        </div>
                        <div class="info-item">
                            <label>Ngày cấp:</label>
                            <span class="info-value">@Model.IssueDate.ToString("dd/MM/yyyy HH:mm")</span>
                        </div>
                        <div class="info-item">
                            <label>Trạng thái file:</label>
                            @if (Model.HasCertificateFile)
                            {
                                <span class="badge bg-success">Có file đính kèm</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Không có file</span>
                            }
                        </div>
                    </div>

                    @if (Model.HasCertificateFile)
                    {
                        <div class="file-info mt-3">
                            <h6 class="section-title">
                                <i class="fas fa-file-pdf text-danger"></i>
                                File chứng chỉ:
                            </h6>
                            <div class="d-flex align-items-center gap-2">
                                <span class="file-url">@Model.CertificateUrl</span>
                                <a href="@Model.CertificateUrl" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-external-link-alt"></i> Xem
                                </a>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Lưu ý: File chứng chỉ sẽ không bị xóa khỏi hệ thống lưu trữ, chỉ liên kết bị gỡ bỏ.
                            </small>
                        </div>
                    }
                </div>

                <!-- Consequences -->
                <div class="consequences mt-4">
                    <h6 class="section-title">
                        <i class="fas fa-exclamation-circle text-warning"></i>
                        Hậu quả khi xóa:
                    </h6>
                    <ul class="consequence-list">
                        <li>Chứng chỉ sẽ bị xóa vĩnh viễn khỏi hệ thống</li>
                        <li>Học viên sẽ không thể truy cập chứng chỉ này nữa</li>
                        <li>Thông tin chứng chỉ sẽ không xuất hiện trong báo cáo</li>
                        <li>Không thể khôi phục dữ liệu sau khi xóa</li>
                        @if (Model.HasCertificateFile)
                        {
                            <li>File PDF chứng chỉ vẫn tồn tại nhưng không thể truy cập qua hệ thống</li>
                        }
                    </ul>
                </div>

                <!-- Alternative Actions -->
                <div class="alternatives mt-4">
                    <h6 class="section-title">
                        <i class="fas fa-lightbulb text-info"></i>
                        Thay vì xóa, bạn có thể:
                    </h6>
                    <div class="alternative-actions">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Chỉnh sửa thông tin
                        </a>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="regenerateCertificate(@Model.Id)">
                            <i class="fas fa-sync"></i> Tạo lại số chứng chỉ
                        </button>
                    </div>
                </div>

                <!-- Confirmation Form -->
                <form asp-action="Delete" method="post" class="confirmation-form mt-4">
                    <input asp-for="Id" type="hidden">
                    
                    <div class="confirmation-checkbox">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                Tôi hiểu rằng hành động này không thể hoàn tác và xác nhận muốn xóa chứng chỉ này
                            </label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy bỏ
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Enable delete button only when checkbox is checked
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('deleteButton').disabled = !this.checked;
});

function regenerateCertificate(certificateId) {
    if (confirm('Bạn có chắc chắn muốn tạo lại chứng chỉ này thay vì xóa?')) {
        fetch(`/Admin/Certificates/RegenerateCertificate/${certificateId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => {
                    window.location.href = `/Admin/Certificates/Details/${certificateId}`;
                }, 1500);
            } else {
                toastr.error(data.message);
            }
        })
        .catch(error => {
            toastr.error('Có lỗi xảy ra khi tạo lại chứng chỉ');
        });
    }
}
</script>

<style>
.section-title {
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 500;
    color: #495057;
}

.consequence-list {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.consequence-list li {
    margin-bottom: 0.5rem;
    color: #dc3545;
}

.alternative-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.confirmation-form {
    border-top: 2px solid #dee2e6;
    padding-top: 1.5rem;
}

.confirmation-checkbox {
    margin-bottom: 1.5rem;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

.file-url {
    font-family: monospace;
    font-size: 0.9rem;
    color: #6c757d;
    word-break: break-all;
}

.file-info {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 4px solid #dc3545;
}
</style>
