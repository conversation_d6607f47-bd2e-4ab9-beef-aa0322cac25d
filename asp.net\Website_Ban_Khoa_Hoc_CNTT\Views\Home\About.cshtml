@{
    ViewData["Title"] = "Giới thiệu";
}

<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h1 class="display-4 fw-bold">V<PERSON> chúng tôi</h1>
            <p class="lead text-muted">Nền tảng học lập trình CNTT hàng đầu Việt Nam</p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-lg-6 mb-4">
            <h2 class="h3 mb-4"><PERSON><PERSON><PERSON> chuyện của chúng tôi</h2>
            <p class="text-muted mb-4">
                <PERSON><PERSON><PERSON><PERSON> thành lập với sứ mệnh đem đến những khóa học lập trình chất lượng cao, 
                chúng tôi đã trở thành một trong những nền tảng học trực tuyến uy tín nhất trong lĩnh vực CNTT.
            </p>
            <p class="text-muted mb-4">
                Với đội ngũ giảng viên giàu kinh nghiệm và chương trình học được thiết kế khoa học, 
                chúng tôi cam kết mang đến cho học viên những kiến thức thực tế và kỹ năng cần thiết 
                để thành công trong ngành công nghệ thông tin.
            </p>
        </div>
        <div class="col-lg-6 mb-4">
            <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                 alt="About Us" class="img-fluid rounded shadow">
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-bullseye fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">Sứ mệnh</h5>
                    <p class="card-text text-muted">
                        Đào tạo những lập trình viên tài năng, có khả năng thích ứng với 
                        công nghệ mới và đáp ứng nhu cầu của thị trường lao động.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-eye fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">Tầm nhìn</h5>
                    <p class="card-text text-muted">
                        Trở thành nền tảng giáo dục CNTT hàng đầu khu vực, 
                        góp phần phát triển nguồn nhân lực công nghệ chất lượng cao.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-heart fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">Giá trị cốt lõi</h5>
                    <p class="card-text text-muted">
                        Chất lượng, sáng tạo, và cam kết mang đến trải nghiệm học tập 
                        tốt nhất cho mọi học viên.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 text-center">
            <div class="bg-primary text-white rounded p-5">
                <h3 class="mb-3">Sẵn sàng bắt đầu hành trình học tập?</h3>
                <p class="mb-4">Tham gia cùng hàng nghìn học viên đã thành công với chúng tôi</p>
                <a href="@Url.Action("Courses", "Home")" class="btn btn-light btn-lg">
                    <i class="fas fa-rocket me-2"></i>Khám phá khóa học
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }
    
    .bg-primary {
        background: linear-gradient(135deg, var(--primary-color), #667eea) !important;
    }
</style>
