using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using ELearningWebsite.Data;
using ELearningWebsite.Models;

namespace ELearningWebsite.Controllers
{
    [Authorize]
    public class EnrollmentController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<EnrollmentController> _logger;

        public EnrollmentController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<EnrollmentController> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: Enrollment/Checkout/5
        public async Task<IActionResult> Checkout(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Category)
                .Include(c => c.Discounts.Where(d => d.IsActive &&
                    (!d.StartDate.HasValue || d.StartDate <= DateTime.Now) &&
                    (!d.EndDate.HasValue || d.EndDate >= DateTime.Now) &&
                    d.CurrentUses < d.MaxUses))
                .FirstOrDefaultAsync(c => c.Id == id);

            if (course == null)
            {
                return NotFound();
            }

            // Kiểm tra xem user đã đăng ký khóa học này chưa
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToAction("Login", "Account", new { area = "Identity" });
            }

            var existingEnrollment = await _context.Enrollments
                .FirstOrDefaultAsync(e => e.CourseId == id); // e.UserId == currentUser.Id && // Tạm comment vì UserId là int, currentUser.Id là string

            if (existingEnrollment != null)
            {
                TempData["ErrorMessage"] = "Bạn đã đăng ký khóa học này rồi!";
                return RedirectToAction("CourseDetail", "Home", new { id = id });
            }

            var viewModel = new CheckoutViewModel
            {
                Course = course,
                OriginalPrice = course.Price,
                FinalPrice = course.Price,
                DiscountAmount = 0,
                AppliedDiscount = null
            };

            // Áp dụng discount tự động nếu có
            var autoDiscount = course.Discounts.FirstOrDefault();
            if (autoDiscount != null)
            {
                var discountAmount = (double)(course.Price * (double)autoDiscount.DiscountPer / 100);
                viewModel.DiscountAmount = discountAmount;
                viewModel.FinalPrice = course.Price - discountAmount;
                viewModel.AppliedDiscount = autoDiscount;
            }

            return View(viewModel);
        }

        // POST: Enrollment/ProcessEnrollment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ProcessEnrollment(int courseId, string? discountCode = null)
        {
            try
            {
                var course = await _context.Courses.FindAsync(courseId);
                if (course == null)
                {
                    return Json(new { success = false, message = "Khóa học không tồn tại!" });
                }

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { success = false, message = "Vui lòng đăng nhập!" });
                }

                // Kiểm tra đã đăng ký chưa
                var existingEnrollment = await _context.Enrollments
                    .FirstOrDefaultAsync(e => e.CourseId == courseId); // e.UserId == currentUser.Id && // Tạm comment vì UserId là int, currentUser.Id là string

                if (existingEnrollment != null)
                {
                    return Json(new { success = false, message = "Bạn đã đăng ký khóa học này rồi!" });
                }

                // Xử lý khóa học miễn phí
                if (course.Price == 0)
                {
                    var enrollment = new Enrollment
                    {
                        UserId = 1, // currentUser.Id, // Tạm hardcode vì UserId là int, currentUser.Id là string
                        CourseId = courseId,
                        EnrollmentDate = DateTime.Now,
                        Status = 1, // Active
                        Progress = 0,
                        ExpiredDate = course.LimitDay.HasValue ? DateTime.Now.AddDays(course.LimitDay.Value) : null
                    };

                    _context.Enrollments.Add(enrollment);
                    await _context.SaveChangesAsync();

                    // Ghi log tài chính
                    await LogFinanceRecord(course.Price, "Free Course Enrollment", currentUser.UserName ?? "Unknown");

                    return Json(new {
                        success = true,
                        message = "Đăng ký khóa học miễn phí thành công!",
                        redirectUrl = Url.Action("MyCourses", "User")
                    });
                }

                // Xử lý khóa học có phí - chuyển đến trang thanh toán
                return Json(new {
                    success = true,
                    message = "Chuyển đến trang thanh toán...",
                    redirectUrl = Url.Action("Payment", "Enrollment", new { courseId = courseId, discountCode = discountCode })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing enrollment for course {CourseId}", courseId);
                return Json(new { success = false, message = "Có lỗi xảy ra, vui lòng thử lại!" });
            }
        }

        // GET: Enrollment/Payment/5
        public async Task<IActionResult> Payment(int courseId, string? discountCode = null)
        {
            var course = await _context.Courses
                .Include(c => c.Category)
                .FirstOrDefaultAsync(c => c.Id == courseId);

            if (course == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToAction("Login", "Account", new { area = "Identity" });
            }

            var viewModel = new PaymentViewModel
            {
                Course = course,
                OriginalPrice = course.Price,
                FinalPrice = course.Price,
                DiscountAmount = 0,
                DiscountCode = discountCode,
                UserEmail = currentUser.Email ?? "",
                UserName = currentUser.FullName
            };

            // Áp dụng mã giảm giá nếu có
            if (!string.IsNullOrEmpty(discountCode))
            {
                var discount = await _context.Discounts
                    .FirstOrDefaultAsync(d => d.Code == discountCode &&
                        d.CourseId == courseId &&
                        d.IsActive &&
                        d.CurrentUses < d.MaxUses &&
                        (!d.StartDate.HasValue || d.StartDate <= DateTime.Now) &&
                        (!d.EndDate.HasValue || d.EndDate >= DateTime.Now));

                if (discount != null)
                {
                    var discountAmount = (double)(course.Price * (double)discount.DiscountPer / 100);
                    viewModel.DiscountAmount = discountAmount;
                    viewModel.FinalPrice = course.Price - discountAmount;
                    viewModel.AppliedDiscount = discount;
                }
            }

            return View(viewModel);
        }

        // POST: Enrollment/ProcessPayment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ProcessPayment(PaymentViewModel model)
        {
            try
            {
                var course = await _context.Courses.FindAsync(model.CourseId);
                if (course == null)
                {
                    TempData["ErrorMessage"] = "Khóa học không tồn tại!";
                    return RedirectToAction("Courses", "Home");
                }

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Account", new { area = "Identity" });
                }

                // Tạo enrollment record
                var enrollment = new Enrollment
                {
                    UserId = 1, // currentUser.Id, // Tạm hardcode vì UserId là int, currentUser.Id là string
                    CourseId = model.CourseId,
                    EnrollmentDate = DateTime.Now,
                    Status = 1, // Active
                    Progress = 0,
                    ExpiredDate = course.LimitDay.HasValue ? DateTime.Now.AddDays(course.LimitDay.Value) : null
                };

                _context.Enrollments.Add(enrollment);

                // Cập nhật số lần sử dụng discount nếu có
                if (!string.IsNullOrEmpty(model.DiscountCode))
                {
                    var discount = await _context.Discounts
                        .FirstOrDefaultAsync(d => d.Code == model.DiscountCode && d.CourseId == model.CourseId);

                    if (discount != null)
                    {
                        discount.CurrentUses++;
                        _context.Discounts.Update(discount);
                    }
                }

                // Ghi log tài chính
                await LogFinanceRecord(model.FinalPrice, "Course Purchase", currentUser.UserName ?? "Unknown");

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Thanh toán và đăng ký khóa học thành công!";
                return RedirectToAction("PaymentSuccess", new { enrollmentId = enrollment.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for course {CourseId}", model.CourseId);
                TempData["ErrorMessage"] = "Có lỗi xảy ra trong quá trình thanh toán!";
                return RedirectToAction("Payment", new { courseId = model.CourseId, discountCode = model.DiscountCode });
            }
        }

        // GET: Enrollment/PaymentSuccess/5
        public async Task<IActionResult> PaymentSuccess(int enrollmentId)
        {
            var enrollment = await _context.Enrollments
                .Include(e => e.Course)
                .ThenInclude(c => c.Category)
                .FirstOrDefaultAsync(e => e.Id == enrollmentId);

            if (enrollment == null)
            {
                return NotFound();
            }

            // Tạm thời redirect về MyCourses vì view PaymentSuccess có lỗi
            TempData["SuccessMessage"] = "Thanh toán thành công! Khóa học đã được kích hoạt.";
            return RedirectToAction("MyCourses", "User");
        }

        private async Task LogFinanceRecord(double amount, string description, string userName)
        {
            var now = DateTime.Now;
            var finance = new Finance
            {
                Month = now.Month,
                Year = now.Year,
                Revenue = amount,
                Fee = 0,
                Type = "Course Sale",
                Description = description,
                CreatedAt = now,
                UpdatedAt = now,
                CreatedBy = userName,
                UpdatedBy = userName
            };

            _context.Finances.Add(finance);
        }
    }

    // ViewModels
    public class CheckoutViewModel
    {
        public Course Course { get; set; } = null!;
        public double OriginalPrice { get; set; }
        public double FinalPrice { get; set; }
        public double DiscountAmount { get; set; }
        public Discount? AppliedDiscount { get; set; }
    }

    public class PaymentViewModel
    {
        public int CourseId { get; set; }
        public Course Course { get; set; } = null!;
        public double OriginalPrice { get; set; }
        public double FinalPrice { get; set; }
        public double DiscountAmount { get; set; }
        public string? DiscountCode { get; set; }
        public Discount? AppliedDiscount { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = "bank_transfer";
    }
}
